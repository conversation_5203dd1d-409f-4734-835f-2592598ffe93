#ifndef STORAGE_H
#define STORAGE_H

#include <stdint.h>
#include <stdbool.h>

/**
 * @brief  Initializes the storage module.
 *         (Currently does nothing, but good practice to have).
 */
void storage_init(void);

/**
 * @brief  Writes the distance calibration offset to Flash memory.
 * @param  offset The 16-bit offset value to save.
 * @return bool true if write was successful, false otherwise.
 */
bool storage_write_offset(int16_t offset);

/**
 * @brief  Reads the distance calibration offset from Flash memory.
 * @return The stored 16-bit offset value. If no value is stored, returns 0.
 */
int16_t storage_read_offset(void);

#endif /* STORAGE_H */ 
