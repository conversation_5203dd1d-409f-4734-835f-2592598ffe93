<html>
<body>
<pre>
<h1>�Vision Build Log</h1>
<h2>Tool Versions:</h2>
IDE-Version: ��Vision V5.24.2.0
Copyright (C) 2017 ARM Ltd and ARM Germany GmbH. All rights reserved.
License Information: 1 PC, 1, LIC=8VRJ7-ECJWW-VE7HA-17XLF-ZPP1V-BKZ11
 
Tool Versions:
Toolchain:       MDK-ARM Plus  Version: 5.24.1
Toolchain Path:  D:\keil5\ARM\ARMCC\Bin
C Compiler:      Armcc.exe V5.06 update 5 (build 528)
Assembler:       Armasm.exe V5.06 update 5 (build 528)
Linker/Locator:  ArmLink.exe V5.06 update 5 (build 528)
Library Manager: ArmAr.exe V5.06 update 5 (build 528)
Hex Converter:   FromElf.exe V5.06 update 5 (build 528)
CPU DLL:         SARMCM3.DLL V5.24.1
Dialog DLL:      DCM.DLL V1.16.0.0
Target DLL:      STLink\ST-LINKIII-KEIL_SWO.dll V3.0.1.0
Dialog DLL:      TCM.DLL V1.32.0.0
 
<h2>Project:</h2>
D:\TDuser\Desktop\3-4 ��������LED\Project.uvprojx
Project File Date:  06/26/2025

<h2>Output:</h2>
*** Using Compiler 'V5.06 update 5 (build 528)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Target 1'
".\Objects\Project.axf" - 0 Error(s), 0 Warning(s).

<h2>Software Packages used:</h2>

Package Vendor: Keil
                http://www.keil.com/pack/Keil.STM32F1xx_DFP.2.2.0.pack
                Keil.STM32F1xx_DFP.2.2.0
                STMicroelectronics STM32F1 Series Device Support, Drivers and Examples

<h2>Collection of Component include folders:</h2>
  .\RTE\_Target_1
  D:\keil5\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Device\Include

<h2>Collection of Component Files used:</h2>
Build Time Elapsed:  00:00:01
</pre>
</body>
</html>
