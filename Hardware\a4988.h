#ifndef __A4988_H
#define __A4988_H

#if !defined(__cplusplus)
  #if !defined(bool)
    typedef unsigned char bool;
    #define true 1
    #define false 0
  #endif
#endif

#include <stdint.h>

// ================= 步进电机参数 =================
// 步进电机每转步数（常见为200步/圈）
#define STEPS_PER_REV 200 // 步进电机一圈的步数

// ================= 电机引脚定义 =================
// 电机1 STEP脉冲引脚
#define STEPPER1_STEP_PIN    GPIO_Pin_0 // 电机1脉冲引脚
// 电机1 方向引脚
#define STEPPER1_DIR_PIN     GPIO_Pin_4 // 电机1方向引脚
// 电机1 使能引脚
#define STEPPER1_ENABLE_PIN  GPIO_Pin_5 // 电机1使能引脚
// 电机1 所在端口
#define STEPPER1_PORT        GPIOA      // 电机1端口

// 电机2 STEP脉冲引脚
#define STEPPER2_STEP_PIN    GPIO_Pin_1 // 电机2脉冲引脚
// 电机2 方向引脚
#define STEPPER2_DIR_PIN     GPIO_Pin_6 // 电机2方向引脚
// 电机2 使能引脚
#define STEPPER2_ENABLE_PIN  GPIO_Pin_7 // 电机2使能引脚
// 电机2 所在端口
#define STEPPER2_PORT        GPIOA      // 电机2端口

// ================= 电机1状态结构体 =================
/**
 * @brief 步进电机1状态结构体
 * @note  用于软件计数法控制电机1脉冲
 */
typedef struct {
    volatile uint32_t interval; ///< 脉冲间隔(10us单位)
    volatile uint32_t counter;  ///< 当前计数值
    volatile uint8_t flag;      ///< 脉冲标志（可用于主循环触发）
    volatile int32_t steps;     ///< 总步数（可正可负，含方向）
    volatile bool direction;    ///< 当前方向（true为正转，false为反转）
    volatile bool enabled;      ///< 使能状态（true为使能，false为禁用）
} Stepper1_State_t;

// ================= 电机2状态结构体 =================
/**
 * @brief 步进电机2状态结构体
 * @note  用于定时器PWM法，并支持平滑加减速
 */
typedef struct {
    volatile uint16_t current_rpm;  ///< 当前转速 (RPM)
    volatile uint16_t target_rpm;   ///< 目标转速 (RPM)
    volatile uint16_t accel_step;   ///< 加速度步进 (每次更新增加的RPM)
    volatile uint16_t update_interval; ///< 速度更新间隔 (tick单位)
    volatile uint16_t update_counter;  ///< 速度更新计数器
    volatile int32_t steps;         ///< 总步数（由TIM2_IRQHandler更新）
    volatile bool direction;        ///< 当前方向
    volatile bool enabled;          ///< 使能状态
} Stepper2_State_t;

// ================= 公共API函数声明 =================
/**
 * @brief  步进电机初始化，配置引脚和定时器
 */
void a4988_init(void); // 初始化A4988相关硬件

/**
 * @brief  设置指定电机方向
 * @param  stepper_id 电机编号（1或2）
 * @param  direction  方向（true为正转，false为反转）
 */
void a4988_set_direction(uint8_t stepper_id, bool direction); // 设置电机方向

/**
 * @brief  使能或禁用指定电机
 * @param  stepper_id 电机编号（1或2）
 * @param  enable     true为使能，false为禁用
 */
void a4988_enable(uint8_t stepper_id, bool enable); // 使能/禁用电机

/**
 * @brief  获取指定电机累计转角（单位：度）
 * @param  stepper_id 电机编号（1或2）
 * @return 当前累计角度（可正可负）
 */
int32_t a4988_get_angle(uint8_t stepper_id); // 获取电机累计角度

/**
 * @brief  重置指定电机累计角度/步数
 * @param  stepper_id 电机编号（1或2）
 */
void a4988_reset_angle(uint8_t stepper_id); // 角度/步数归零

// ================= 电机1专用API =================
/**
 * @brief  设置电机1转速（软件计数法）
 * @param  rpm 目标转速（转/分钟）
 */
void a4988_set_speed1(uint16_t rpm); // 设置电机1转速

/**
 * @brief  电机1主循环脉冲处理（基于SysTick/软件计数）
 * @note   需在主循环中高频率调用
 */
void stepper1_process(void); // 电机1主循环脉冲处理（SysTick）

/**
 * @brief  电机1主循环脉冲处理（软件计数法）
 * @note   需在主循环中高频率调用
 */
void stepper1_software_tick(void); // 电机1主循环脉冲处理（软件计数）

/**
 * @brief  启动电机1步数运动（非阻塞）
 * @param  steps 目标步数（正为正转，负为反转）
 */
void a4988_start_move_steps1(int32_t steps); // 电机1步数运动

/**
 * @brief  启动电机1步数运动（非阻塞，带自定义速度）
 * @param  steps 目标步数（正为正转，负为反转）
 * @param  speed 运动速度（RPM）
 */
void a4988_start_move_steps1_with_speed(int32_t steps, uint16_t speed); // 电机1步数运动（自定义速度）

// ================= 电机2专用API =================
/**
 * @brief  设置电机2转速（PWM法，带加减速）
 * @param  rpm 目标转速（转/分钟）
 */
void a4988_set_speed2(uint16_t rpm); // 设置电机2转速

/**
 * @brief  电机2主循环加减速处理
 * @note   需在主循环中高频率调用
 */
void stepper2_acceleration_tick(void); // 电机2主循环加减速处理

/**
 * @brief  启动电机2步数运动（非阻塞）
 * @param  steps 目标步数（正为正转，负为反转）
 */
void a4988_start_move_steps2(int32_t steps); // 电机2步数运动

/**
 * @brief  启动电机2步数运动（非阻塞，带自定义速度）
 * @param  steps 目标步数（正为正转，负为反转）
 * @param  speed 运动速度（RPM）
 */
void a4988_start_move_steps2_with_speed(int32_t steps, uint16_t speed); // 电机2步数运动（自定义速度）

/**
 * @brief  PWM测试函数（用于验证PWM输出）
 * @note   直接输出1kHz，50%占空比的PWM信号
 */
void a4988_test_pwm(void); // PWM测试

/**
 * @brief  获取电机状态结构体指针
 */
extern Stepper1_State_t stepper1_state; // 电机1状态结构体
extern Stepper2_State_t stepper2_state; // 电机2状态结构体

// ====== 新增：步数运动相关外部变量 ======
extern volatile int32_t stepper1_target_steps; // 电机1目标步数
extern volatile int32_t stepper1_start_step;   // 电机1运动起点
extern volatile uint8_t stepper1_busy;         // 电机1忙标志
extern volatile int32_t stepper2_target_pulses; // 电机2目标脉冲数
extern volatile int32_t stepper2_start_pulse;   // 电机2运动起点
extern volatile uint8_t stepper2_busy;          // 电机2忙标志
extern volatile int32_t stepper2_pulse_count;   // 电机2脉冲计数（用于精确步数控制）

int32_t degrees_to_steps(int32_t degrees);

// ================= 电机2平缓调速脉冲控制函数 =================
void motor2_smooth_pulses(uint32_t pulse_count, uint32_t time_ms, uint16_t max_freq);
void motor2_smooth_update(void);
uint8_t motor2_is_done(void);
uint32_t motor2_get_remaining(void);
uint16_t motor2_get_current_freq(void);
uint8_t motor2_get_phase(void);
uint32_t motor2_rpm_to_ppm(uint16_t rpm);
void motor2_move_degrees_smooth(int32_t degrees, uint32_t time_ms, uint16_t max_rpm);
void motor2_move_degrees(int32_t degrees, uint16_t rpm);

// ================= 电机同步控制函数 =================
void motors_start_sync_move(int32_t motor1_steps, uint16_t motor1_speed, int32_t motor2_degrees);
void motors_sync_update(void);
uint8_t motors_sync_is_done(void);
uint8_t motors_get_sync_progress(float *motor1_progress, float *motor2_progress);
uint8_t motors_get_sync_speed_info(uint16_t *current_freq, uint16_t *target_freq, int32_t *pulse_diff);

#endif /* __A4988_H */
