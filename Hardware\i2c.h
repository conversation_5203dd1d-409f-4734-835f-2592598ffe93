#ifndef I2C_H
#define I2C_H

#include <stdbool.h>
#include <stdint.h>
#include "stm32f10x.h"

/* 
 * 软件I2C引脚定义
 * 注意：你可以将这些引脚更改为任何支持开漏输出的GPIO引脚。
 */
#define I2C_PORT                GPIOB // 软件I2C使用的GPIO端口
#define I2C_SCL_PIN             GPIO_Pin_6
#define I2C_SDA_PIN             GPIO_Pin_7
#define I2C_GPIO_CLK            RCC_APB2Periph_GPIOB
#define I2C_CLK                RCC_APB1Periph_I2C1
#define I2C                    I2C1

/* I2C 时钟频率定义 */
#define I2C_SPEED              100000  // 100KHz

/* I2C 超时定义 */
#define I2C_TIMEOUT            1000    // 超时时间（单位：循环次数）

/**
 * @brief 初始化软件I2C所需的GPIO引脚
 * @return bool 总是返回 true
 */
bool i2c_init(void); // 初始化I2C引脚

/**
 * @brief 设置I2C从设备地址
 * @param addr 7位从设备地址
 */
void i2c_set_slave_address(uint8_t addr);

/**
 * @brief 从指定地址读取一个字节
 * @param reg_addr 寄存器地址
 * @param data 数据指针
 * @return bool 读取是否成功
 */
bool i2c_read_addr8_data8(uint8_t reg_addr, uint8_t *data); // 读取8位数据

/**
 * @brief 从指定地址读取两个字节
 * @param reg_addr 寄存器地址
 * @param data 数据指针
 * @return bool 读取是否成功
 */
bool i2c_read_addr8_data16(uint8_t reg_addr, uint16_t *data);

/**
 * @brief 从指定地址读取32位数据
 * @param reg_addr 寄存器地址
 * @param data 数据指针
 * @return bool 读取是否成功
 */
bool i2c_read_addr8_data32(uint8_t reg_addr, uint32_t *data);

/**
 * @brief 从指定地址读取多个字节
 * @param reg_addr 寄存器地址
 * @param data 数据缓冲区
 * @param len 要读取的字节数
 * @return bool 读取是否成功
 */
bool i2c_read_addr8_bytes(uint8_t reg_addr, uint8_t *data, uint8_t len);

/**
 * @brief 向指定地址写入一个字节
 * @param reg_addr 寄存器地址
 * @param data 要写入的数据
 * @return bool 写入是否成功
 */
bool i2c_write_addr8_data8(uint8_t reg_addr, uint8_t data);

/**
 * @brief 向指定地址写入多个字节
 * @param reg_addr 寄存器地址
 * @param data 数据缓冲区
 * @param len 要写入的字节数
 * @return bool 写入是否成功
 */
bool i2c_write_addr8_bytes(uint8_t reg_addr, const uint8_t *data, uint8_t len);

#endif /* I2C_H */ 
