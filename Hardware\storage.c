#include "storage.h"
#include "stm32f10x.h"

// We will use the last page of the Flash memory to store our data.
// For STM32F103C8 (64KB Flash), the last page starts at address 0x0800FC00.
// The page size is 1KB.
#define FLASH_STORAGE_ADDRESS   0x0800FC00

// We will use the first 2 bytes (16 bits) of the page to store the offset.
#define OFFSET_ADDRESS          (FLASH_STORAGE_ADDRESS)

void storage_init(void) {
    // This function is currently empty but can be used for future initializations.
}

bool storage_write_offset(int16_t offset) {
    FLASH_Unlock();
    
    // Erase the page before writing to it
    FLASH_Status status = FLASH_ErasePage(FLASH_STORAGE_ADDRESS);
    if (status != FLASH_COMPLETE) {
        FLASH_Lock();
        return false;
    }
    
    // Program the 16-bit offset value (as a HalfWord)
    status = FLASH_ProgramHalfWord(OFFSET_ADDRESS, (uint16_t)offset);
    
    FLASH_Lock();
    
    return (status == FLASH_COMPLETE);
}

int16_t storage_read_offset(void) {
    // Read the 16-bit value from the specified Flash address
    uint16_t stored_value = *(volatile uint16_t*)OFFSET_ADDRESS;
    
    // If the flash has been erased, it will read 0xFFFF. 
    // In this case, there's no valid offset stored, so we return 0.
    if (stored_value == 0xFFFF) {
        return 0;
    }
    
    return (int16_t)stored_value;
} 
