#ifndef __USART_H
#define __USART_H

#include "stm32f10x.h"

/**
 * @brief USART configuration structure
 */
typedef struct {
    uint32_t baudRate;     /**< Baud rate for communication */
    uint16_t dataBits;     /**< Number of data bits (8 or 9) */
    uint16_t stopBits;     /**< Number of stop bits (1 or 2) */
    uint16_t parity;       /**< Parity mode (0: None, 1: Even, 2: Odd) */
    uint16_t flowControl;  /**< Flow control (0: None, 1: RTS/CTS) */
} UsartConfig_t;

/**
 * @brief USART status enumeration
 */
typedef enum {
    USART_OK = 0,          /**< Operation successful */
    USART_ERROR,           /**< General error */
    USART_TIMEOUT,         /**< Operation timeout */
    USART_BUSY             /**< USART is busy */
} UsartStatus_t;

/**
 * @brief Initialize USART peripheral
 * @param usartConfig Pointer to USART configuration structure
 * @return UsartStatus_t Status of initialization
 */
UsartStatus_t usartInit(const UsartConfig_t* usartConfig);

/**
 * @brief Send a single byte via USART
 * @param data Byte to send
 * @return UsartStatus_t Status of transmission
 */
UsartStatus_t usartSendByte(uint8_t data);

/**
 * @brief Send multiple bytes via USART
 * @param data Pointer to data buffer
 * @param length Number of bytes to send
 * @return UsartStatus_t Status of transmission
 */
UsartStatus_t usartSendData(const uint8_t* data, uint16_t length);

/**
 * @brief Send a string via USART
 * @param string Null-terminated string to send
 * @return UsartStatus_t Status of transmission
 */
UsartStatus_t usartSendString(const char* string);

/**
 * @brief Receive a single byte via USART
 * @param data Pointer to store received byte
 * @param timeout Timeout in milliseconds (0 for no timeout)
 * @return UsartStatus_t Status of reception
 */
UsartStatus_t usartReceiveByte(uint8_t* data, uint32_t timeout);

/**
 * @brief Receive multiple bytes via USART
 * @param data Pointer to data buffer
 * @param length Number of bytes to receive
 * @param timeout Timeout in milliseconds (0 for no timeout)
 * @return UsartStatus_t Status of reception
 */
UsartStatus_t usartReceiveData(uint8_t* data, uint16_t length, uint32_t timeout);

/**
 * @brief Check if USART is ready to send data
 * @return uint8_t True if ready to send
 */
uint8_t usartIsReadyToSend(void);

/**
 * @brief Check if USART has received data
 * @return uint8_t True if data is available
 */
uint8_t usartHasData(void);

/**
 * @brief Enable USART receive interrupt
 * @return UsartStatus_t Status of operation
 */
UsartStatus_t usartEnableReceiveInterrupt(void);

/**
 * @brief Disable USART receive interrupt
 * @return UsartStatus_t Status of operation
 */
UsartStatus_t usartDisableReceiveInterrupt(void);

/**
 * @brief Set receive callback function
 * @param callback Function pointer to callback
 */
void usartSetReceiveCallback(void (*callback)(uint8_t data));

/**
 * @brief Process USART interrupt (to be called from main loop or timer)
 * @return uint8_t True if interrupt was processed
 */
uint8_t usartProcessInterrupt(void);

/**
 * @brief Get a received byte if available (non-blocking)
 * @param data Pointer to store received byte
 * @return uint8_t True if data was available
 */
uint8_t usartGetReceivedByte(uint8_t* data);

#endif /* __USART_H */
