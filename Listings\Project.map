Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to delay.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to a4988.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    delay.o(i.Delay_GetTick) refers to delay.o(.data) for system_tick_ms
    delay.o(i.Delay_Init) refers to delay.o(i.NVIC_SetPriority) for NVIC_SetPriority
    delay.o(i.Delay_Init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    delay.o(i.Delay_us) refers to delay.o(.data) for delay_state
    delay.o(i.SysTick_Handler) refers to a4988.o(i.stepper1_software_tick) for stepper1_software_tick
    delay.o(i.SysTick_Handler) refers to delay.o(.data) for delay_state
    key.o(i.Key_GetNum) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_GetNum) refers to delay.o(i.Delay_ms) for Delay_ms
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    a4988.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    a4988.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    a4988.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    a4988.o(i.TIM2_IRQHandler) refers to a4988.o(i.a4988_enable) for a4988_enable
    a4988.o(i.TIM2_IRQHandler) refers to a4988.o(.bss) for stepper2_state
    a4988.o(i.TIM2_IRQHandler) refers to a4988.o(.data) for stepper2_pulse_count
    a4988.o(i._a4988_test_pwm) refers to stm32f10x_tim.o(i.TIM_SetAutoreload) for TIM_SetAutoreload
    a4988.o(i._a4988_test_pwm) refers to stm32f10x_tim.o(i.TIM_SetCompare2) for TIM_SetCompare2
    a4988.o(i._a4988_test_pwm) refers to stm32f10x_tim.o(i.TIM_GenerateEvent) for TIM_GenerateEvent
    a4988.o(i._a4988_update_pwm) refers to stm32f10x_tim.o(i.TIM_SetCompare2) for TIM_SetCompare2
    a4988.o(i._a4988_update_pwm) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    a4988.o(i._a4988_update_pwm) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    a4988.o(i._a4988_update_pwm) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    a4988.o(i._a4988_update_pwm) refers to stm32f10x_tim.o(i.TIM_SetAutoreload) for TIM_SetAutoreload
    a4988.o(i._a4988_update_pwm) refers to stm32f10x_tim.o(i.TIM_GenerateEvent) for TIM_GenerateEvent
    a4988.o(i.a4988_enable) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    a4988.o(i.a4988_enable) refers to a4988.o(i.a4988_set_speed2) for a4988_set_speed2
    a4988.o(i.a4988_enable) refers to a4988.o(i._a4988_update_pwm) for _a4988_update_pwm
    a4988.o(i.a4988_enable) refers to a4988.o(.bss) for stepper1_state
    a4988.o(i.a4988_get_angle) refers to a4988.o(.bss) for stepper1_state
    a4988.o(i.a4988_get_angle) refers to a4988.o(.data) for stepper2_pulse_count
    a4988.o(i.a4988_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    a4988.o(i.a4988_init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    a4988.o(i.a4988_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    a4988.o(i.a4988_init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    a4988.o(i.a4988_init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    a4988.o(i.a4988_init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    a4988.o(i.a4988_init) refers to stm32f10x_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    a4988.o(i.a4988_init) refers to stm32f10x_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    a4988.o(i.a4988_init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    a4988.o(i.a4988_init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    a4988.o(i.a4988_init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    a4988.o(i.a4988_init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    a4988.o(i.a4988_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    a4988.o(i.a4988_init) refers to a4988.o(.bss) for stepper1_state
    a4988.o(i.a4988_reset_angle) refers to a4988.o(.bss) for stepper1_state
    a4988.o(i.a4988_reset_angle) refers to a4988.o(.data) for stepper2_pulse_count
    a4988.o(i.a4988_set_direction) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    a4988.o(i.a4988_set_direction) refers to a4988.o(.bss) for stepper1_state
    a4988.o(i.a4988_set_speed1) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    a4988.o(i.a4988_set_speed1) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    a4988.o(i.a4988_set_speed1) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    a4988.o(i.a4988_set_speed1) refers to a4988.o(.bss) for stepper1_state
    a4988.o(i.a4988_set_speed2) refers to a4988.o(.bss) for stepper2_state
    a4988.o(i.a4988_start_move_steps1) refers to a4988.o(i.a4988_start_move_steps1_with_speed) for a4988_start_move_steps1_with_speed
    a4988.o(i.a4988_start_move_steps1_with_speed) refers to a4988.o(i.a4988_set_direction) for a4988_set_direction
    a4988.o(i.a4988_start_move_steps1_with_speed) refers to a4988.o(i.a4988_enable) for a4988_enable
    a4988.o(i.a4988_start_move_steps1_with_speed) refers to a4988.o(i.a4988_set_speed1) for a4988_set_speed1
    a4988.o(i.a4988_start_move_steps1_with_speed) refers to a4988.o(.bss) for stepper1_state
    a4988.o(i.a4988_start_move_steps1_with_speed) refers to a4988.o(.data) for stepper1_start_step
    a4988.o(i.a4988_start_move_steps2) refers to a4988.o(i.a4988_start_move_steps2_with_speed) for a4988_start_move_steps2_with_speed
    a4988.o(i.a4988_start_move_steps2_with_speed) refers to a4988.o(i.a4988_set_direction) for a4988_set_direction
    a4988.o(i.a4988_start_move_steps2_with_speed) refers to a4988.o(i.a4988_enable) for a4988_enable
    a4988.o(i.a4988_start_move_steps2_with_speed) refers to a4988.o(i.a4988_set_speed2) for a4988_set_speed2
    a4988.o(i.a4988_start_move_steps2_with_speed) refers to a4988.o(.data) for stepper2_pulse_count
    a4988.o(i.a4988_test_pwm) refers to a4988.o(i._a4988_test_pwm) for _a4988_test_pwm
    a4988.o(i.motor2_get_current_freq) refers to a4988.o(.bss) for motor2_smooth
    a4988.o(i.motor2_get_phase) refers to a4988.o(.bss) for motor2_smooth
    a4988.o(i.motor2_get_remaining) refers to a4988.o(.bss) for motor2_smooth
    a4988.o(i.motor2_is_done) refers to a4988.o(.bss) for motor2_smooth
    a4988.o(i.motor2_move_degrees) refers to a4988.o(i.motor2_move_degrees_smooth) for motor2_move_degrees_smooth
    a4988.o(i.motor2_move_degrees_smooth) refers to a4988.o(i.motor2_smooth_pulses) for motor2_smooth_pulses
    a4988.o(i.motor2_move_degrees_smooth) refers to a4988.o(i.a4988_set_direction) for a4988_set_direction
    a4988.o(i.motor2_smooth_pulses) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    a4988.o(i.motor2_smooth_pulses) refers to a4988.o(i.a4988_enable) for a4988_enable
    a4988.o(i.motor2_smooth_pulses) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    a4988.o(i.motor2_smooth_pulses) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    a4988.o(i.motor2_smooth_pulses) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    a4988.o(i.motor2_smooth_pulses) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    a4988.o(i.motor2_smooth_pulses) refers to delay.o(i.Delay_GetTick) for Delay_GetTick
    a4988.o(i.motor2_smooth_pulses) refers to stm32f10x_tim.o(i.TIM_SetAutoreload) for TIM_SetAutoreload
    a4988.o(i.motor2_smooth_pulses) refers to stm32f10x_tim.o(i.TIM_SetCompare2) for TIM_SetCompare2
    a4988.o(i.motor2_smooth_pulses) refers to a4988.o(.bss) for motor2_smooth
    a4988.o(i.motor2_smooth_update) refers to delay.o(i.Delay_GetTick) for Delay_GetTick
    a4988.o(i.motor2_smooth_update) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    a4988.o(i.motor2_smooth_update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    a4988.o(i.motor2_smooth_update) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    a4988.o(i.motor2_smooth_update) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    a4988.o(i.motor2_smooth_update) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    a4988.o(i.motor2_smooth_update) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    a4988.o(i.motor2_smooth_update) refers to stm32f10x_tim.o(i.TIM_SetAutoreload) for TIM_SetAutoreload
    a4988.o(i.motor2_smooth_update) refers to stm32f10x_tim.o(i.TIM_SetCompare2) for TIM_SetCompare2
    a4988.o(i.motor2_smooth_update) refers to a4988.o(.bss) for motor2_smooth
    a4988.o(i.motor2_smooth_update) refers to a4988.o(.data) for last_update_time
    a4988.o(i.motors_get_sync_progress) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    a4988.o(i.motors_get_sync_progress) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    a4988.o(i.motors_get_sync_progress) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    a4988.o(i.motors_get_sync_progress) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    a4988.o(i.motors_get_sync_progress) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    a4988.o(i.motors_get_sync_progress) refers to a4988.o(.bss) for motor_sync
    a4988.o(i.motors_get_sync_speed_info) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    a4988.o(i.motors_get_sync_speed_info) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    a4988.o(i.motors_get_sync_speed_info) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    a4988.o(i.motors_get_sync_speed_info) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    a4988.o(i.motors_get_sync_speed_info) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    a4988.o(i.motors_get_sync_speed_info) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    a4988.o(i.motors_get_sync_speed_info) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    a4988.o(i.motors_get_sync_speed_info) refers to a4988.o(.bss) for motor_sync
    a4988.o(i.motors_start_sync_move) refers to a4988.o(i.a4988_set_direction) for a4988_set_direction
    a4988.o(i.motors_start_sync_move) refers to a4988.o(i.a4988_start_move_steps1_with_speed) for a4988_start_move_steps1_with_speed
    a4988.o(i.motors_start_sync_move) refers to a4988.o(i.motor2_smooth_pulses) for motor2_smooth_pulses
    a4988.o(i.motors_start_sync_move) refers to delay.o(i.Delay_GetTick) for Delay_GetTick
    a4988.o(i.motors_start_sync_move) refers to a4988.o(.bss) for motor_sync
    a4988.o(i.motors_sync_is_done) refers to a4988.o(.bss) for motor_sync
    a4988.o(i.motors_sync_is_done) refers to a4988.o(.data) for stepper1_busy
    a4988.o(i.motors_sync_update) refers to delay.o(i.Delay_GetTick) for Delay_GetTick
    a4988.o(i.motors_sync_update) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    a4988.o(i.motors_sync_update) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    a4988.o(i.motors_sync_update) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    a4988.o(i.motors_sync_update) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    a4988.o(i.motors_sync_update) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    a4988.o(i.motors_sync_update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    a4988.o(i.motors_sync_update) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    a4988.o(i.motors_sync_update) refers to stm32f10x_tim.o(i.TIM_SetAutoreload) for TIM_SetAutoreload
    a4988.o(i.motors_sync_update) refers to stm32f10x_tim.o(i.TIM_SetCompare2) for TIM_SetCompare2
    a4988.o(i.motors_sync_update) refers to a4988.o(i.motor2_smooth_update) for motor2_smooth_update
    a4988.o(i.motors_sync_update) refers to a4988.o(.bss) for motor_sync
    a4988.o(i.motors_sync_update) refers to a4988.o(.data) for last_sync_update
    a4988.o(i.stepper1_software_tick) refers to a4988.o(i.a4988_set_speed1) for a4988_set_speed1
    a4988.o(i.stepper1_software_tick) refers to a4988.o(i.a4988_enable) for a4988_enable
    a4988.o(i.stepper1_software_tick) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    a4988.o(i.stepper1_software_tick) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    a4988.o(i.stepper1_software_tick) refers to a4988.o(.bss) for stepper1_state
    a4988.o(i.stepper1_software_tick) refers to a4988.o(.data) for stepper1_busy
    a4988.o(i.stepper2_acceleration_tick) refers to a4988.o(i.a4988_set_speed2) for a4988_set_speed2
    a4988.o(i.stepper2_acceleration_tick) refers to a4988.o(i.a4988_enable) for a4988_enable
    a4988.o(i.stepper2_acceleration_tick) refers to a4988.o(i._a4988_update_pwm) for _a4988_update_pwm
    a4988.o(i.stepper2_acceleration_tick) refers to a4988.o(.bss) for stepper2_state
    a4988.o(i.stepper2_acceleration_tick) refers to a4988.o(.data) for stepper2_busy
    gpio.o(i.gpio_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    gpio.o(i.gpio_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    gpio.o(i.gpio_init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    gpio.o(i.gpio_set_output) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    gpio.o(i.gpio_set_output) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    i2c.o(i.i2c_ack) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    i2c.o(i.i2c_ack) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_ack) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    i2c.o(i.i2c_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    i2c.o(i.i2c_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    i2c.o(i.i2c_init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    i2c.o(i.i2c_nack) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    i2c.o(i.i2c_nack) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    i2c.o(i.i2c_nack) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_read_addr8_bytes) refers to i2c.o(i.i2c_start) for i2c_start
    i2c.o(i.i2c_read_addr8_bytes) refers to i2c.o(i.i2c_send_byte) for i2c_send_byte
    i2c.o(i.i2c_read_addr8_bytes) refers to i2c.o(i.i2c_wait_ack) for i2c_wait_ack
    i2c.o(i.i2c_read_addr8_bytes) refers to i2c.o(i.i2c_stop) for i2c_stop
    i2c.o(i.i2c_read_addr8_bytes) refers to i2c.o(i.i2c_read_byte) for i2c_read_byte
    i2c.o(i.i2c_read_addr8_bytes) refers to i2c.o(.data) for current_slave_addr
    i2c.o(i.i2c_read_addr8_data16) refers to i2c.o(i.i2c_read_addr8_bytes) for i2c_read_addr8_bytes
    i2c.o(i.i2c_read_addr8_data32) refers to i2c.o(i.i2c_read_addr8_bytes) for i2c_read_addr8_bytes
    i2c.o(i.i2c_read_addr8_data8) refers to i2c.o(i.i2c_start) for i2c_start
    i2c.o(i.i2c_read_addr8_data8) refers to i2c.o(i.i2c_send_byte) for i2c_send_byte
    i2c.o(i.i2c_read_addr8_data8) refers to i2c.o(i.i2c_wait_ack) for i2c_wait_ack
    i2c.o(i.i2c_read_addr8_data8) refers to i2c.o(i.i2c_stop) for i2c_stop
    i2c.o(i.i2c_read_addr8_data8) refers to i2c.o(i.i2c_read_byte) for i2c_read_byte
    i2c.o(i.i2c_read_addr8_data8) refers to i2c.o(.data) for current_slave_addr
    i2c.o(i.i2c_read_byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    i2c.o(i.i2c_read_byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    i2c.o(i.i2c_read_byte) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_read_byte) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    i2c.o(i.i2c_read_byte) refers to i2c.o(i.i2c_ack) for i2c_ack
    i2c.o(i.i2c_read_byte) refers to i2c.o(i.i2c_nack) for i2c_nack
    i2c.o(i.i2c_send_byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    i2c.o(i.i2c_send_byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    i2c.o(i.i2c_send_byte) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_set_slave_address) refers to i2c.o(.data) for current_slave_addr
    i2c.o(i.i2c_start) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    i2c.o(i.i2c_start) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_start) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    i2c.o(i.i2c_stop) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    i2c.o(i.i2c_stop) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_stop) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    i2c.o(i.i2c_wait_ack) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    i2c.o(i.i2c_wait_ack) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    i2c.o(i.i2c_wait_ack) refers to i2c.o(i.i2c_delay) for i2c_delay
    i2c.o(i.i2c_wait_ack) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    i2c.o(i.i2c_write_addr8_bytes) refers to i2c.o(i.i2c_start) for i2c_start
    i2c.o(i.i2c_write_addr8_bytes) refers to i2c.o(i.i2c_send_byte) for i2c_send_byte
    i2c.o(i.i2c_write_addr8_bytes) refers to i2c.o(i.i2c_wait_ack) for i2c_wait_ack
    i2c.o(i.i2c_write_addr8_bytes) refers to i2c.o(i.i2c_stop) for i2c_stop
    i2c.o(i.i2c_write_addr8_bytes) refers to i2c.o(.data) for current_slave_addr
    i2c.o(i.i2c_write_addr8_data8) refers to i2c.o(i.i2c_start) for i2c_start
    i2c.o(i.i2c_write_addr8_data8) refers to i2c.o(i.i2c_send_byte) for i2c_send_byte
    i2c.o(i.i2c_write_addr8_data8) refers to i2c.o(i.i2c_wait_ack) for i2c_wait_ack
    i2c.o(i.i2c_write_addr8_data8) refers to i2c.o(i.i2c_stop) for i2c_stop
    i2c.o(i.i2c_write_addr8_data8) refers to i2c.o(.data) for current_slave_addr
    vl53l0x.o(i.configure_address) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.configure_gpio) refers to gpio.o(i.gpio_init) for gpio_init
    vl53l0x.o(i.configure_gpio) refers to gpio.o(i.gpio_set_output) for gpio_set_output
    vl53l0x.o(i.configure_interrupt) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.configure_interrupt) refers to i2c.o(i.i2c_read_addr8_data8) for i2c_read_addr8_data8
    vl53l0x.o(i.data_init) refers to i2c.o(i.i2c_read_addr8_data8) for i2c_read_addr8_data8
    vl53l0x.o(i.data_init) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.data_init) refers to vl53l0x.o(.data) for stop_variable
    vl53l0x.o(i.device_is_booted) refers to i2c.o(i.i2c_read_addr8_data8) for i2c_read_addr8_data8
    vl53l0x.o(i.get_spad_info_from_nvm) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.get_spad_info_from_nvm) refers to i2c.o(i.i2c_read_addr8_data8) for i2c_read_addr8_data8
    vl53l0x.o(i.get_spad_info_from_nvm) refers to vl53l0x.o(i.read_strobe) for read_strobe
    vl53l0x.o(i.get_spad_info_from_nvm) refers to i2c.o(i.i2c_read_addr8_data32) for i2c_read_addr8_data32
    vl53l0x.o(i.get_spad_info_from_nvm) refers to i2c.o(i.i2c_read_addr8_bytes) for i2c_read_addr8_bytes
    vl53l0x.o(i.init_address) refers to vl53l0x.o(i.set_hardware_standby) for set_hardware_standby
    vl53l0x.o(i.init_address) refers to i2c.o(i.i2c_set_slave_address) for i2c_set_slave_address
    vl53l0x.o(i.init_address) refers to delay.o(i.Delay_ms) for Delay_ms
    vl53l0x.o(i.init_address) refers to vl53l0x.o(i.device_is_booted) for device_is_booted
    vl53l0x.o(i.init_address) refers to vl53l0x.o(i.configure_address) for configure_address
    vl53l0x.o(i.init_address) refers to vl53l0x.o(.constdata) for vl53l0x_infos
    vl53l0x.o(i.init_addresses) refers to vl53l0x.o(i.configure_gpio) for configure_gpio
    vl53l0x.o(i.init_addresses) refers to vl53l0x.o(i.init_address) for init_address
    vl53l0x.o(i.init_config) refers to i2c.o(i.i2c_set_slave_address) for i2c_set_slave_address
    vl53l0x.o(i.init_config) refers to vl53l0x.o(i.data_init) for data_init
    vl53l0x.o(i.init_config) refers to vl53l0x.o(i.static_init) for static_init
    vl53l0x.o(i.init_config) refers to vl53l0x.o(i.perform_ref_calibration) for perform_ref_calibration
    vl53l0x.o(i.init_config) refers to vl53l0x.o(.constdata) for vl53l0x_infos
    vl53l0x.o(i.load_default_tuning_settings) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.perform_ref_calibration) refers to vl53l0x.o(i.perform_single_ref_calibration) for perform_single_ref_calibration
    vl53l0x.o(i.perform_ref_calibration) refers to vl53l0x.o(i.set_sequence_steps_enabled) for set_sequence_steps_enabled
    vl53l0x.o(i.perform_single_ref_calibration) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.perform_single_ref_calibration) refers to i2c.o(i.i2c_read_addr8_data8) for i2c_read_addr8_data8
    vl53l0x.o(i.read_strobe) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.read_strobe) refers to i2c.o(i.i2c_read_addr8_data8) for i2c_read_addr8_data8
    vl53l0x.o(i.set_hardware_standby) refers to gpio.o(i.gpio_set_output) for gpio_set_output
    vl53l0x.o(i.set_hardware_standby) refers to vl53l0x.o(.constdata) for vl53l0x_infos
    vl53l0x.o(i.set_sequence_steps_enabled) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.set_spads_from_nvm) refers to vl53l0x.o(i.get_spad_info_from_nvm) for get_spad_info_from_nvm
    vl53l0x.o(i.set_spads_from_nvm) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.set_spads_from_nvm) refers to i2c.o(i.i2c_write_addr8_bytes) for i2c_write_addr8_bytes
    vl53l0x.o(i.static_init) refers to vl53l0x.o(i.set_spads_from_nvm) for set_spads_from_nvm
    vl53l0x.o(i.static_init) refers to vl53l0x.o(i.load_default_tuning_settings) for load_default_tuning_settings
    vl53l0x.o(i.static_init) refers to vl53l0x.o(i.configure_interrupt) for configure_interrupt
    vl53l0x.o(i.static_init) refers to vl53l0x.o(i.set_sequence_steps_enabled) for set_sequence_steps_enabled
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(i.init_addresses) for init_addresses
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(i.init_config) for init_config
    vl53l0x.o(i.vl53l0x_read_range_adaptive) refers to vl53l0x.o(i.vl53l0x_read_range_single) for vl53l0x_read_range_single
    vl53l0x.o(i.vl53l0x_read_range_adaptive) refers to delay.o(i.Delay_ms) for Delay_ms
    vl53l0x.o(i.vl53l0x_read_range_average) refers to vl53l0x.o(i.vl53l0x_read_range_single) for vl53l0x_read_range_single
    vl53l0x.o(i.vl53l0x_read_range_average) refers to delay.o(i.Delay_ms) for Delay_ms
    vl53l0x.o(i.vl53l0x_read_range_continuous) refers to vl53l0x.o(i.vl53l0x_read_range_single) for vl53l0x_read_range_single
    vl53l0x.o(i.vl53l0x_read_range_continuous) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    vl53l0x.o(i.vl53l0x_read_range_continuous) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    vl53l0x.o(i.vl53l0x_read_range_continuous) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    vl53l0x.o(i.vl53l0x_read_range_continuous) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    vl53l0x.o(i.vl53l0x_read_range_continuous) refers to vl53l0x.o(.data) for first_measurement
    vl53l0x.o(i.vl53l0x_read_range_high_precision) refers to vl53l0x.o(i.vl53l0x_read_range_single) for vl53l0x_read_range_single
    vl53l0x.o(i.vl53l0x_read_range_high_precision) refers to delay.o(i.Delay_ms) for Delay_ms
    vl53l0x.o(i.vl53l0x_read_range_single) refers to i2c.o(i.i2c_set_slave_address) for i2c_set_slave_address
    vl53l0x.o(i.vl53l0x_read_range_single) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.vl53l0x_read_range_single) refers to i2c.o(i.i2c_read_addr8_data8) for i2c_read_addr8_data8
    vl53l0x.o(i.vl53l0x_read_range_single) refers to i2c.o(i.i2c_read_addr8_data16) for i2c_read_addr8_data16
    vl53l0x.o(i.vl53l0x_read_range_single) refers to vl53l0x.o(.constdata) for vl53l0x_infos
    vl53l0x.o(i.vl53l0x_read_range_single) refers to vl53l0x.o(.data) for stop_variable
    vl53l0x.o(i.vl53l0x_set_mode) refers to i2c.o(i.i2c_set_slave_address) for i2c_set_slave_address
    vl53l0x.o(i.vl53l0x_set_mode) refers to i2c.o(i.i2c_write_addr8_data8) for i2c_write_addr8_data8
    vl53l0x.o(i.vl53l0x_set_mode) refers to vl53l0x.o(.constdata) for vl53l0x_infos
    usart.o(i.usartDisableReceiveInterrupt) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.usartDisableReceiveInterrupt) refers to usart.o(.data) for isInitialized
    usart.o(i.usartEnableReceiveInterrupt) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.usartEnableReceiveInterrupt) refers to usart.o(.data) for isInitialized
    usart.o(i.usartGetReceivedByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.usartGetReceivedByte) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.usartGetReceivedByte) refers to usart.o(.data) for isInitialized
    usart.o(i.usartHasData) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.usartHasData) refers to usart.o(.data) for isInitialized
    usart.o(i.usartInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.usartInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.usartInit) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.usartInit) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.usartInit) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.usartInit) refers to usart.o(.data) for isInitialized
    usart.o(i.usartIsReadyToSend) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.usartIsReadyToSend) refers to usart.o(.data) for isInitialized
    usart.o(i.usartProcessInterrupt) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.usartProcessInterrupt) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i.usartProcessInterrupt) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.usartProcessInterrupt) refers to usart.o(.data) for isInitialized
    usart.o(i.usartReceiveByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.usartReceiveByte) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.usartReceiveByte) refers to usart.o(.data) for isInitialized
    usart.o(i.usartReceiveData) refers to usart.o(i.usartReceiveByte) for usartReceiveByte
    usart.o(i.usartReceiveData) refers to usart.o(.data) for isInitialized
    usart.o(i.usartSendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.usartSendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.usartSendByte) refers to usart.o(.data) for isInitialized
    usart.o(i.usartSendData) refers to usart.o(i.usartSendByte) for usartSendByte
    usart.o(i.usartSendData) refers to usart.o(.data) for isInitialized
    usart.o(i.usartSendString) refers to usart.o(i.usartSendData) for usartSendData
    usart.o(i.usartSendString) refers to usart.o(.data) for isInitialized
    usart.o(i.usartSetReceiveCallback) refers to usart.o(.data) for receiveCallback
    storage.o(i.storage_write_offset) refers to stm32f10x_flash.o(i.FLASH_Unlock) for FLASH_Unlock
    storage.o(i.storage_write_offset) refers to stm32f10x_flash.o(i.FLASH_ErasePage) for FLASH_ErasePage
    storage.o(i.storage_write_offset) refers to stm32f10x_flash.o(i.FLASH_Lock) for FLASH_Lock
    storage.o(i.storage_write_offset) refers to stm32f10x_flash.o(i.FLASH_ProgramHalfWord) for FLASH_ProgramHalfWord
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    oled.o(i.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(i.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i.OLED_DrawEllipse) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_GPIO_Init) for OLED_GPIO_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_W_SCL) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_W_SDA) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    main.o(i.auto_run_state_machine) refers to vl53l0x.o(i.vl53l0x_read_range_single) for vl53l0x_read_range_single
    main.o(i.auto_run_state_machine) refers to a4988.o(i.a4988_get_angle) for a4988_get_angle
    main.o(i.auto_run_state_machine) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.auto_run_state_machine) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.auto_run_state_machine) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.auto_run_state_machine) refers to oled.o(i.OLED_ShowSignedNum) for OLED_ShowSignedNum
    main.o(i.auto_run_state_machine) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.auto_run_state_machine) refers to a4988.o(i.a4988_reset_angle) for a4988_reset_angle
    main.o(i.auto_run_state_machine) refers to usart.o(i.usartSendString) for usartSendString
    main.o(i.auto_run_state_machine) refers to a4988.o(i.motors_start_sync_move) for motors_start_sync_move
    main.o(i.auto_run_state_machine) refers to main.o(i.print_number) for print_number
    main.o(i.auto_run_state_machine) refers to main.o(.data) for autoRunState
    main.o(i.auto_run_state_machine) refers to main.o(.bss) for recorded_data
    main.o(i.auto_run_state_machine) refers to a4988.o(.data) for stepper2_pulse_count
    main.o(i.auto_run_state_machine) refers to a4988.o(i.motors_sync_is_done) for motors_sync_is_done
    main.o(i.auto_run_state_machine) refers to a4988.o(i.a4988_start_move_steps1) for a4988_start_move_steps1
    main.o(i.auto_run_state_machine) refers to a4988.o(i.stepper1_software_tick) for stepper1_software_tick
    main.o(i.auto_run_state_machine) refers to a4988.o(i.a4988_set_speed1) for a4988_set_speed1
    main.o(i.auto_run_state_machine) refers to a4988.o(i.a4988_enable) for a4988_enable
    main.o(i.auto_run_state_machine) refers to a4988.o(i.degrees_to_steps) for degrees_to_steps
    main.o(i.auto_run_state_machine) refers to a4988.o(i.a4988_start_move_steps2) for a4988_start_move_steps2
    main.o(i.auto_run_state_machine) refers to a4988.o(i.stepper2_acceleration_tick) for stepper2_acceleration_tick
    main.o(i.auto_run_state_machine) refers to a4988.o(i.motors_get_sync_progress) for motors_get_sync_progress
    main.o(i.auto_run_state_machine) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(i.auto_run_state_machine) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    main.o(i.auto_run_state_machine) refers to a4988.o(i.motors_get_sync_speed_info) for motors_get_sync_speed_info
    main.o(i.main) refers to a4988.o(i.a4988_init) for a4988_init
    main.o(i.main) refers to delay.o(i.Delay_Init) for Delay_Init
    main.o(i.main) refers to i2c.o(i.i2c_init) for i2c_init
    main.o(i.main) refers to storage.o(i.storage_init) for storage_init
    main.o(i.main) refers to key.o(i.Key_Init) for Key_Init
    main.o(i.main) refers to usart.o(i.usartInit) for usartInit
    main.o(i.main) refers to usart.o(i.usartSendString) for usartSendString
    main.o(i.main) refers to i2c.o(i.i2c_set_slave_address) for i2c_set_slave_address
    main.o(i.main) refers to i2c.o(i.i2c_read_addr8_data8) for i2c_read_addr8_data8
    main.o(i.main) refers to usart.o(i.usartSendByte) for usartSendByte
    main.o(i.main) refers to vl53l0x.o(i.vl53l0x_init) for vl53l0x_init
    main.o(i.main) refers to vl53l0x.o(i.vl53l0x_read_range_single) for vl53l0x_read_range_single
    main.o(i.main) refers to main.o(i.print_number) for print_number
    main.o(i.main) refers to delay.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to a4988.o(i.a4988_set_direction) for a4988_set_direction
    main.o(i.main) refers to a4988.o(i.a4988_enable) for a4988_enable
    main.o(i.main) refers to a4988.o(i.a4988_get_angle) for a4988_get_angle
    main.o(i.main) refers to main.o(.constdata) for .constdata
    main.o(i.main) refers to storage.o(i.storage_read_offset) for storage_read_offset
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.main) refers to a4988.o(i.a4988_set_speed2) for a4988_set_speed2
    main.o(i.main) refers to main.o(i.auto_run_state_machine) for auto_run_state_machine
    main.o(i.main) refers to a4988.o(i.stepper1_software_tick) for stepper1_software_tick
    main.o(i.main) refers to a4988.o(i.motors_sync_update) for motors_sync_update
    main.o(i.main) refers to oled.o(i.OLED_ShowSignedNum) for OLED_ShowSignedNum
    main.o(i.main) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.main) refers to key.o(i.Key_GetNum) for Key_GetNum
    main.o(i.main) refers to usart.o(i.usartGetReceivedByte) for usartGetReceivedByte
    main.o(i.main) refers to a4988.o(i.a4988_reset_angle) for a4988_reset_angle
    main.o(i.main) refers to main.o(.data) for autoRunState
    main.o(i.main) refers to main.o(.bss) for recorded_data
    main.o(i.main) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    main.o(i.main) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    main.o(i.main) refers to vl53l0x.o(i.vl53l0x_read_range_average) for vl53l0x_read_range_average
    main.o(i.main) refers to storage.o(i.storage_write_offset) for storage_write_offset
    main.o(i.main) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to main.o(.conststring) for .conststring
    main.o(i.main) refers to vl53l0x.o(i.vl53l0x_read_range_high_precision) for vl53l0x_read_range_high_precision
    main.o(i.main) refers to vl53l0x.o(i.vl53l0x_read_range_adaptive) for vl53l0x_read_range_adaptive
    main.o(i.main) refers to vl53l0x.o(i.vl53l0x_read_range_continuous) for vl53l0x_read_range_continuous
    main.o(i.print_number) refers to usart.o(i.usartSendByte) for usartSendByte
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (84 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing a4988.o(i._a4988_test_pwm), (36 bytes).
    Removing a4988.o(i.a4988_test_pwm), (8 bytes).
    Removing a4988.o(i.motor2_get_current_freq), (12 bytes).
    Removing a4988.o(i.motor2_get_phase), (12 bytes).
    Removing a4988.o(i.motor2_get_remaining), (32 bytes).
    Removing a4988.o(i.motor2_is_done), (20 bytes).
    Removing a4988.o(i.motor2_move_degrees), (80 bytes).
    Removing a4988.o(i.motor2_move_degrees_smooth), (96 bytes).
    Removing a4988.o(i.motor2_rpm_to_ppm), (8 bytes).
    Removing vl53l0x.o(i.vl53l0x_set_mode), (388 bytes).
    Removing usart.o(i.usartDisableReceiveInterrupt), (48 bytes).
    Removing usart.o(i.usartEnableReceiveInterrupt), (48 bytes).
    Removing usart.o(i.usartHasData), (40 bytes).
    Removing usart.o(i.usartIsReadyToSend), (40 bytes).
    Removing usart.o(i.usartProcessInterrupt), (88 bytes).
    Removing usart.o(i.usartReceiveByte), (92 bytes).
    Removing usart.o(i.usartReceiveData), (64 bytes).
    Removing usart.o(i.usartSetReceiveCallback), (12 bytes).
    Removing oled.o(i.OLED_DrawArc), (618 bytes).
    Removing oled.o(i.OLED_DrawCircle), (352 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (812 bytes).
    Removing oled.o(i.OLED_DrawLine), (374 bytes).
    Removing oled.o(i.OLED_DrawPoint), (80 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (142 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (232 bytes).
    Removing oled.o(i.OLED_GetPoint), (68 bytes).
    Removing oled.o(i.OLED_IsInAngle), (124 bytes).
    Removing oled.o(i.OLED_Printf), (50 bytes).
    Removing oled.o(i.OLED_Reverse), (52 bytes).
    Removing oled.o(i.OLED_ReverseArea), (168 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (70 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (210 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (96 bytes).
    Removing oled.o(i.OLED_UpdateArea), (124 bytes).
    Removing oled.o(i.OLED_pnpoly), (140 bytes).

477 unused section(s) (total 22452 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    Hardware\Key.c                           0x00000000   Number         0  key.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\OLED_Data.c                     0x00000000   Number         0  oled_data.o ABSOLUTE
    Hardware\a4988.c                         0x00000000   Number         0  a4988.o ABSOLUTE
    Hardware\gpio.c                          0x00000000   Number         0  gpio.o ABSOLUTE
    Hardware\i2c.c                           0x00000000   Number         0  i2c.o ABSOLUTE
    Hardware\storage.c                       0x00000000   Number         0  storage.o ABSOLUTE
    Hardware\usart.c                         0x00000000   Number         0  usart.o ABSOLUTE
    Hardware\vl53l0x.c                       0x00000000   Number         0  vl53l0x.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000160   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000162   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000164   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000166   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000168   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000168   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000168   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800016e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800016e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000172   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800017a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800017c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800017c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000180   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000188   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001c8   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000216   Section        0  heapauxi.o(.text)
    .text                                    0x0800021c   Section        8  libspace.o(.text)
    .text                                    0x08000224   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800026e   Section        0  exit.o(.text)
    .text                                    0x08000280   Section        0  sys_exit.o(.text)
    .text                                    0x0800028c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800028e   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x0800028e   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000292   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Delay_GetTick                          0x08000294   Section        0  delay.o(i.Delay_GetTick)
    i.Delay_Init                             0x080002a0   Section        0  delay.o(i.Delay_Init)
    i.Delay_ms                               0x080002f4   Section        0  delay.o(i.Delay_ms)
    i.Delay_us                               0x0800030c   Section        0  delay.o(i.Delay_us)
    i.FLASH_ErasePage                        0x08000328   Section        0  stm32f10x_flash.o(i.FLASH_ErasePage)
    i.FLASH_GetBank1Status                   0x08000374   Section        0  stm32f10x_flash.o(i.FLASH_GetBank1Status)
    i.FLASH_Lock                             0x080003a8   Section        0  stm32f10x_flash.o(i.FLASH_Lock)
    i.FLASH_ProgramHalfWord                  0x080003bc   Section        0  stm32f10x_flash.o(i.FLASH_ProgramHalfWord)
    i.FLASH_Unlock                           0x080003fc   Section        0  stm32f10x_flash.o(i.FLASH_Unlock)
    i.FLASH_WaitForLastOperation             0x08000414   Section        0  stm32f10x_flash.o(i.FLASH_WaitForLastOperation)
    i.GPIO_Init                              0x0800043a   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x08000550   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ReadOutputDataBit                 0x08000562   Section        0  stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit)
    i.GPIO_ResetBits                         0x08000574   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000578   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x0800057c   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.HardFault_Handler                      0x08000586   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Key_GetNum                             0x0800058c   Section        0  key.o(i.Key_GetNum)
    i.Key_Init                               0x080005e8   Section        0  key.o(i.Key_Init)
    i.MemManage_Handler                      0x08000614   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08000618   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x0800061c   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_SetPriority                       0x0800068c   Section        0  delay.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x0800068d   Thumb Code    32  delay.o(i.NVIC_SetPriority)
    i.OLED_Clear                             0x080006b4   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x080006dc   Section        0  oled.o(i.OLED_ClearArea)
    i.OLED_GPIO_Init                         0x08000784   Section        0  oled.o(i.OLED_GPIO_Init)
    i.OLED_I2C_SendByte                      0x080007e4   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08000822   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x0800083e   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x08000854   Section        0  oled.o(i.OLED_Init)
    i.OLED_Pow                               0x080008ee   Section        0  oled.o(i.OLED_Pow)
    i.OLED_SetCursor                         0x08000902   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08000924   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x08000978   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowNum                           0x08000aa4   Section        0  oled.o(i.OLED_ShowNum)
    i.OLED_ShowSignedNum                     0x08000af0   Section        0  oled.o(i.OLED_ShowSignedNum)
    i.OLED_ShowString                        0x08000b60   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x08000b90   Section        0  oled.o(i.OLED_Update)
    i.OLED_W_SCL                             0x08000bb8   Section        0  oled.o(i.OLED_W_SCL)
    i.OLED_W_SDA                             0x08000be0   Section        0  oled.o(i.OLED_W_SDA)
    i.OLED_WriteCommand                      0x08000c08   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08000c28   Section        0  oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x08000c56   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08000c58   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000c78   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000c98   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08000d6c   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08000d6e   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08000d6f   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08000d78   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08000d79   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x08000e58   Section        0  delay.o(i.SysTick_Handler)
    i.SystemInit                             0x08000e9c   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08000efc   Section        0  a4988.o(i.TIM2_IRQHandler)
    i.TIM_ARRPreloadConfig                   0x08000f84   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_ClearITPendingBit                  0x08000f9c   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08000fa2   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x08000fba   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_GenerateEvent                      0x08000fd8   Section        0  stm32f10x_tim.o(i.TIM_GenerateEvent)
    i.TIM_GetITStatus                        0x08000fdc   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08000ffe   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_OC2Init                            0x08001010   Section        0  stm32f10x_tim.o(i.TIM_OC2Init)
    i.TIM_OC2PreloadConfig                   0x080010b4   Section        0  stm32f10x_tim.o(i.TIM_OC2PreloadConfig)
    i.TIM_SetAutoreload                      0x080010ce   Section        0  stm32f10x_tim.o(i.TIM_SetAutoreload)
    i.TIM_SetCompare2                        0x080010d2   Section        0  stm32f10x_tim.o(i.TIM_SetCompare2)
    i.TIM_TimeBaseInit                       0x080010d8   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x0800117c   Section        0  stm32f10x_it.o(i.USART1_IRQHandler)
    i.USART_Cmd                              0x0800117e   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08001196   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_Init                             0x080011b0   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001288   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08001292   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x0800129a   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i._a4988_update_pwm                      0x080012a0   Section        0  a4988.o(i._a4988_update_pwm)
    _a4988_update_pwm                        0x080012a1   Thumb Code   120  a4988.o(i._a4988_update_pwm)
    i.a4988_enable                           0x08001320   Section        0  a4988.o(i.a4988_enable)
    i.a4988_get_angle                        0x08001380   Section        0  a4988.o(i.a4988_get_angle)
    i.a4988_init                             0x080013b4   Section        0  a4988.o(i.a4988_init)
    i.a4988_reset_angle                      0x080014e8   Section        0  a4988.o(i.a4988_reset_angle)
    i.a4988_set_direction                    0x0800150c   Section        0  a4988.o(i.a4988_set_direction)
    i.a4988_set_speed1                       0x08001554   Section        0  a4988.o(i.a4988_set_speed1)
    i.a4988_set_speed2                       0x0800159c   Section        0  a4988.o(i.a4988_set_speed2)
    i.a4988_start_move_steps1                0x080015a8   Section        0  a4988.o(i.a4988_start_move_steps1)
    i.a4988_start_move_steps1_with_speed     0x080015b8   Section        0  a4988.o(i.a4988_start_move_steps1_with_speed)
    i.a4988_start_move_steps2                0x0800160c   Section        0  a4988.o(i.a4988_start_move_steps2)
    i.a4988_start_move_steps2_with_speed     0x0800161c   Section        0  a4988.o(i.a4988_start_move_steps2_with_speed)
    i.auto_run_state_machine                 0x08001670   Section        0  main.o(i.auto_run_state_machine)
    i.configure_address                      0x08002610   Section        0  vl53l0x.o(i.configure_address)
    configure_address                        0x08002611   Thumb Code    16  vl53l0x.o(i.configure_address)
    i.configure_gpio                         0x08002620   Section        0  vl53l0x.o(i.configure_gpio)
    configure_gpio                           0x08002621   Thumb Code    32  vl53l0x.o(i.configure_gpio)
    i.configure_interrupt                    0x08002640   Section        0  vl53l0x.o(i.configure_interrupt)
    configure_interrupt                      0x08002641   Thumb Code    78  vl53l0x.o(i.configure_interrupt)
    i.data_init                              0x08002690   Section        0  vl53l0x.o(i.data_init)
    data_init                                0x08002691   Thumb Code   132  vl53l0x.o(i.data_init)
    i.degrees_to_steps                       0x08002718   Section        0  a4988.o(i.degrees_to_steps)
    i.device_is_booted                       0x08002728   Section        0  vl53l0x.o(i.device_is_booted)
    device_is_booted                         0x08002729   Thumb Code    36  vl53l0x.o(i.device_is_booted)
    i.get_spad_info_from_nvm                 0x0800274c   Section        0  vl53l0x.o(i.get_spad_info_from_nvm)
    get_spad_info_from_nvm                   0x0800274d   Thumb Code   286  vl53l0x.o(i.get_spad_info_from_nvm)
    i.gpio_init                              0x0800286c   Section        0  gpio.o(i.gpio_init)
    i.gpio_set_output                        0x080028a0   Section        0  gpio.o(i.gpio_set_output)
    i.i2c_ack                                0x080028c8   Section        0  i2c.o(i.i2c_ack)
    i2c_ack                                  0x080028c9   Thumb Code    44  i2c.o(i.i2c_ack)
    i.i2c_delay                              0x080028f8   Section        0  i2c.o(i.i2c_delay)
    i2c_delay                                0x080028f9   Thumb Code    24  i2c.o(i.i2c_delay)
    i.i2c_init                               0x08002910   Section        0  i2c.o(i.i2c_init)
    i.i2c_nack                               0x0800294c   Section        0  i2c.o(i.i2c_nack)
    i2c_nack                                 0x0800294d   Thumb Code    44  i2c.o(i.i2c_nack)
    i.i2c_read_addr8_bytes                   0x0800297c   Section        0  i2c.o(i.i2c_read_addr8_bytes)
    i.i2c_read_addr8_data16                  0x08002a0c   Section        0  i2c.o(i.i2c_read_addr8_data16)
    i.i2c_read_addr8_data32                  0x08002a34   Section        0  i2c.o(i.i2c_read_addr8_data32)
    i.i2c_read_addr8_data8                   0x08002a6c   Section        0  i2c.o(i.i2c_read_addr8_data8)
    i.i2c_read_byte                          0x08002ae0   Section        0  i2c.o(i.i2c_read_byte)
    i2c_read_byte                            0x08002ae1   Thumb Code    92  i2c.o(i.i2c_read_byte)
    i.i2c_send_byte                          0x08002b40   Section        0  i2c.o(i.i2c_send_byte)
    i2c_send_byte                            0x08002b41   Thumb Code    78  i2c.o(i.i2c_send_byte)
    i.i2c_set_slave_address                  0x08002b94   Section        0  i2c.o(i.i2c_set_slave_address)
    i.i2c_start                              0x08002ba0   Section        0  i2c.o(i.i2c_start)
    i2c_start                                0x08002ba1   Thumb Code    48  i2c.o(i.i2c_start)
    i.i2c_stop                               0x08002bd4   Section        0  i2c.o(i.i2c_stop)
    i2c_stop                                 0x08002bd5   Thumb Code    48  i2c.o(i.i2c_stop)
    i.i2c_wait_ack                           0x08002c08   Section        0  i2c.o(i.i2c_wait_ack)
    i2c_wait_ack                             0x08002c09   Thumb Code    68  i2c.o(i.i2c_wait_ack)
    i.i2c_write_addr8_bytes                  0x08002c50   Section        0  i2c.o(i.i2c_write_addr8_bytes)
    i.i2c_write_addr8_data8                  0x08002cc0   Section        0  i2c.o(i.i2c_write_addr8_data8)
    i.init_address                           0x08002d1c   Section        0  vl53l0x.o(i.init_address)
    init_address                             0x08002d1d   Thumb Code    54  vl53l0x.o(i.init_address)
    i.init_addresses                         0x08002d58   Section        0  vl53l0x.o(i.init_addresses)
    init_addresses                           0x08002d59   Thumb Code    22  vl53l0x.o(i.init_addresses)
    i.init_config                            0x08002d70   Section        0  vl53l0x.o(i.init_config)
    init_config                              0x08002d71   Thumb Code    48  vl53l0x.o(i.init_config)
    i.load_default_tuning_settings           0x08002da4   Section        0  vl53l0x.o(i.load_default_tuning_settings)
    load_default_tuning_settings             0x08002da5   Thumb Code   806  vl53l0x.o(i.load_default_tuning_settings)
    i.main                                   0x080030cc   Section        0  main.o(i.main)
    i.motor2_smooth_pulses                   0x080054e8   Section        0  a4988.o(i.motor2_smooth_pulses)
    i.motor2_smooth_update                   0x08005600   Section        0  a4988.o(i.motor2_smooth_update)
    i.motors_get_sync_progress               0x0800578c   Section        0  a4988.o(i.motors_get_sync_progress)
    i.motors_get_sync_speed_info             0x08005850   Section        0  a4988.o(i.motors_get_sync_speed_info)
    i.motors_start_sync_move                 0x08005900   Section        0  a4988.o(i.motors_start_sync_move)
    i.motors_sync_is_done                    0x080059e4   Section        0  a4988.o(i.motors_sync_is_done)
    i.motors_sync_update                     0x08005a10   Section        0  a4988.o(i.motors_sync_update)
    i.perform_ref_calibration                0x08005be0   Section        0  vl53l0x.o(i.perform_ref_calibration)
    perform_ref_calibration                  0x08005be1   Thumb Code    42  vl53l0x.o(i.perform_ref_calibration)
    i.perform_single_ref_calibration         0x08005c0a   Section        0  vl53l0x.o(i.perform_single_ref_calibration)
    perform_single_ref_calibration           0x08005c0b   Thumb Code   128  vl53l0x.o(i.perform_single_ref_calibration)
    i.print_number                           0x08005c8a   Section        0  main.o(i.print_number)
    i.read_strobe                            0x08005cde   Section        0  vl53l0x.o(i.read_strobe)
    read_strobe                              0x08005cdf   Thumb Code    68  vl53l0x.o(i.read_strobe)
    i.set_hardware_standby                   0x08005d24   Section        0  vl53l0x.o(i.set_hardware_standby)
    set_hardware_standby                     0x08005d25   Thumb Code    22  vl53l0x.o(i.set_hardware_standby)
    i.set_sequence_steps_enabled             0x08005d40   Section        0  vl53l0x.o(i.set_sequence_steps_enabled)
    set_sequence_steps_enabled               0x08005d41   Thumb Code    14  vl53l0x.o(i.set_sequence_steps_enabled)
    i.set_spads_from_nvm                     0x08005d4e   Section        0  vl53l0x.o(i.set_spads_from_nvm)
    set_spads_from_nvm                       0x08005d4f   Thumb Code   258  vl53l0x.o(i.set_spads_from_nvm)
    i.static_init                            0x08005e50   Section        0  vl53l0x.o(i.static_init)
    static_init                              0x08005e51   Thumb Code    48  vl53l0x.o(i.static_init)
    i.stepper1_software_tick                 0x08005e80   Section        0  a4988.o(i.stepper1_software_tick)
    i.stepper2_acceleration_tick             0x08005f44   Section        0  a4988.o(i.stepper2_acceleration_tick)
    i.storage_init                           0x08006078   Section        0  storage.o(i.storage_init)
    i.storage_read_offset                    0x0800607c   Section        0  storage.o(i.storage_read_offset)
    i.storage_write_offset                   0x08006094   Section        0  storage.o(i.storage_write_offset)
    i.usartGetReceivedByte                   0x080060d0   Section        0  usart.o(i.usartGetReceivedByte)
    i.usartInit                              0x08006104   Section        0  usart.o(i.usartInit)
    i.usartSendByte                          0x08006208   Section        0  usart.o(i.usartSendByte)
    i.usartSendData                          0x08006248   Section        0  usart.o(i.usartSendData)
    i.usartSendString                        0x08006280   Section        0  usart.o(i.usartSendString)
    i.vl53l0x_init                           0x080062ac   Section        0  vl53l0x.o(i.vl53l0x_init)
    i.vl53l0x_read_range_adaptive            0x080062c8   Section        0  vl53l0x.o(i.vl53l0x_read_range_adaptive)
    i.vl53l0x_read_range_average             0x080063b6   Section        0  vl53l0x.o(i.vl53l0x_read_range_average)
    i.vl53l0x_read_range_continuous          0x08006420   Section        0  vl53l0x.o(i.vl53l0x_read_range_continuous)
    i.vl53l0x_read_range_high_precision      0x080064e0   Section        0  vl53l0x.o(i.vl53l0x_read_range_high_precision)
    i.vl53l0x_read_range_single              0x080065cc   Section        0  vl53l0x.o(i.vl53l0x_read_range_single)
    x$fpl$fadd                               0x080066c0   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x080066cf   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmpinf                            0x08006784   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x0800679c   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x0800679d   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffix                               0x08006920   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$ffixu                              0x08006958   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x08006998   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x080069c8   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fleqf                              0x080069f0   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08006a58   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08006b5a   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08006be6   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x08006bf0   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$fsub                               0x08006c54   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08006c63   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    .constdata                               0x08006d3e   Section        2  vl53l0x.o(.constdata)
    x$fpl$usenofp                            0x08006d3e   Section        0  usenofp.o(x$fpl$usenofp)
    vl53l0x_infos                            0x08006d3e   Data           2  vl53l0x.o(.constdata)
    .constdata                               0x08006d40   Section     2090  oled_data.o(.constdata)
    .constdata                               0x0800756c   Section       12  main.o(.constdata)
    .conststring                             0x08007578   Section       92  main.o(.conststring)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000014   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000024   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000028   Section        9  delay.o(.data)
    system_tick_ms                           0x2000002c   Data           4  delay.o(.data)
    ms_counter                               0x20000030   Data           1  delay.o(.data)
    .data                                    0x20000034   Section       36  a4988.o(.data)
    last_sync_update                         0x20000050   Data           4  a4988.o(.data)
    last_update_time                         0x20000054   Data           4  a4988.o(.data)
    .data                                    0x20000058   Section        1  i2c.o(.data)
    current_slave_addr                       0x20000058   Data           1  i2c.o(.data)
    .data                                    0x2000005a   Section        5  vl53l0x.o(.data)
    stop_variable                            0x2000005a   Data           1  vl53l0x.o(.data)
    filtered_value                           0x2000005c   Data           2  vl53l0x.o(.data)
    first_measurement                        0x2000005e   Data           1  vl53l0x.o(.data)
    .data                                    0x20000060   Section        6  usart.o(.data)
    receiveCallback                          0x20000060   Data           4  usart.o(.data)
    isInitialized                            0x20000064   Data           1  usart.o(.data)
    interruptEnabled                         0x20000065   Data           1  usart.o(.data)
    .data                                    0x20000068   Section       44  main.o(.data)
    autoRunState                             0x20000068   Data           1  main.o(.data)
    autoMotor1Rounds                         0x2000006c   Data           4  main.o(.data)
    angle1                                   0x20000070   Data           4  main.o(.data)
    angle2                                   0x20000074   Data           4  main.o(.data)
    distance                                 0x20000078   Data           2  main.o(.data)
    step_count                               0x2000007c   Data           4  main.o(.data)
    replay_index                             0x20000080   Data           4  main.o(.data)
    target_angle1                            0x20000084   Data           4  main.o(.data)
    target_angle2                            0x20000088   Data           4  main.o(.data)
    first_entry                              0x2000008c   Data           1  main.o(.data)
    autoTimer                                0x20000090   Data           4  main.o(.data)
    .bss                                     0x20000094   Section      104  a4988.o(.bss)
    .bss                                     0x200000fc   Section     1024  oled.o(.bss)
    .bss                                     0x200004fc   Section      160  main.o(.bss)
    .bss                                     0x2000059c   Section       96  libspace.o(.bss)
    HEAP                                     0x20000600   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x20000600   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x20000800   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x20000800   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x20000c00   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000161   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000165   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000169   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000169   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000169   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800017b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000189   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001a5   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __aeabi_memclr4                          0x080001c9   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080001c9   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080001c9   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080001cd   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000217   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000219   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800021b   Thumb Code     2  heapauxi.o(.text)
    __user_libspace                          0x0800021d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800021d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800021d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000225   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800026f   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x08000281   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800028d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800028d   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x0800028f   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x0800028f   Thumb Code     0  indicate_semi.o(.text)
    DebugMon_Handler                         0x08000293   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Delay_GetTick                            0x08000295   Thumb Code     6  delay.o(i.Delay_GetTick)
    Delay_Init                               0x080002a1   Thumb Code    74  delay.o(i.Delay_Init)
    Delay_ms                                 0x080002f5   Thumb Code    24  delay.o(i.Delay_ms)
    Delay_us                                 0x0800030d   Thumb Code    24  delay.o(i.Delay_us)
    FLASH_ErasePage                          0x08000329   Thumb Code    72  stm32f10x_flash.o(i.FLASH_ErasePage)
    FLASH_GetBank1Status                     0x08000375   Thumb Code    48  stm32f10x_flash.o(i.FLASH_GetBank1Status)
    FLASH_Lock                               0x080003a9   Thumb Code    14  stm32f10x_flash.o(i.FLASH_Lock)
    FLASH_ProgramHalfWord                    0x080003bd   Thumb Code    60  stm32f10x_flash.o(i.FLASH_ProgramHalfWord)
    FLASH_Unlock                             0x080003fd   Thumb Code    12  stm32f10x_flash.o(i.FLASH_Unlock)
    FLASH_WaitForLastOperation               0x08000415   Thumb Code    38  stm32f10x_flash.o(i.FLASH_WaitForLastOperation)
    GPIO_Init                                0x0800043b   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08000551   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ReadOutputDataBit                   0x08000563   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit)
    GPIO_ResetBits                           0x08000575   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000579   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x0800057d   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    HardFault_Handler                        0x08000587   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Key_GetNum                               0x0800058d   Thumb Code    88  key.o(i.Key_GetNum)
    Key_Init                                 0x080005e9   Thumb Code    40  key.o(i.Key_Init)
    MemManage_Handler                        0x08000615   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08000619   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x0800061d   Thumb Code   100  misc.o(i.NVIC_Init)
    OLED_Clear                               0x080006b5   Thumb Code    36  oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x080006dd   Thumb Code   162  oled.o(i.OLED_ClearArea)
    OLED_GPIO_Init                           0x08000785   Thumb Code    92  oled.o(i.OLED_GPIO_Init)
    OLED_I2C_SendByte                        0x080007e5   Thumb Code    62  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08000823   Thumb Code    28  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x0800083f   Thumb Code    22  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x08000855   Thumb Code   154  oled.o(i.OLED_Init)
    OLED_Pow                                 0x080008ef   Thumb Code    20  oled.o(i.OLED_Pow)
    OLED_SetCursor                           0x08000903   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08000925   Thumb Code    74  oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x08000979   Thumb Code   296  oled.o(i.OLED_ShowImage)
    OLED_ShowNum                             0x08000aa5   Thumb Code    76  oled.o(i.OLED_ShowNum)
    OLED_ShowSignedNum                       0x08000af1   Thumb Code   112  oled.o(i.OLED_ShowSignedNum)
    OLED_ShowString                          0x08000b61   Thumb Code    46  oled.o(i.OLED_ShowString)
    OLED_Update                              0x08000b91   Thumb Code    36  oled.o(i.OLED_Update)
    OLED_W_SCL                               0x08000bb9   Thumb Code    36  oled.o(i.OLED_W_SCL)
    OLED_W_SDA                               0x08000be1   Thumb Code    36  oled.o(i.OLED_W_SDA)
    OLED_WriteCommand                        0x08000c09   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08000c29   Thumb Code    46  oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x08000c57   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08000c59   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000c79   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000c99   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08000d6d   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08000e59   Thumb Code    54  delay.o(i.SysTick_Handler)
    SystemInit                               0x08000e9d   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x08000efd   Thumb Code   122  a4988.o(i.TIM2_IRQHandler)
    TIM_ARRPreloadConfig                     0x08000f85   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_ClearITPendingBit                    0x08000f9d   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08000fa3   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x08000fbb   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_GenerateEvent                        0x08000fd9   Thumb Code     4  stm32f10x_tim.o(i.TIM_GenerateEvent)
    TIM_GetITStatus                          0x08000fdd   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08000fff   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_OC2Init                              0x08001011   Thumb Code   154  stm32f10x_tim.o(i.TIM_OC2Init)
    TIM_OC2PreloadConfig                     0x080010b5   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC2PreloadConfig)
    TIM_SetAutoreload                        0x080010cf   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetAutoreload)
    TIM_SetCompare2                          0x080010d3   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare2)
    TIM_TimeBaseInit                         0x080010d9   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x0800117d   Thumb Code     2  stm32f10x_it.o(i.USART1_IRQHandler)
    USART_Cmd                                0x0800117f   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08001197   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_Init                               0x080011b1   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001289   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08001293   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x0800129b   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    a4988_enable                             0x08001321   Thumb Code    84  a4988.o(i.a4988_enable)
    a4988_get_angle                          0x08001381   Thumb Code    42  a4988.o(i.a4988_get_angle)
    a4988_init                               0x080013b5   Thumb Code   294  a4988.o(i.a4988_init)
    a4988_reset_angle                        0x080014e9   Thumb Code    24  a4988.o(i.a4988_reset_angle)
    a4988_set_direction                      0x0800150d   Thumb Code    58  a4988.o(i.a4988_set_direction)
    a4988_set_speed1                         0x08001555   Thumb Code    60  a4988.o(i.a4988_set_speed1)
    a4988_set_speed2                         0x0800159d   Thumb Code     6  a4988.o(i.a4988_set_speed2)
    a4988_start_move_steps1                  0x080015a9   Thumb Code    14  a4988.o(i.a4988_start_move_steps1)
    a4988_start_move_steps1_with_speed       0x080015b9   Thumb Code    68  a4988.o(i.a4988_start_move_steps1_with_speed)
    a4988_start_move_steps2                  0x0800160d   Thumb Code    14  a4988.o(i.a4988_start_move_steps2)
    a4988_start_move_steps2_with_speed       0x0800161d   Thumb Code    68  a4988.o(i.a4988_start_move_steps2_with_speed)
    auto_run_state_machine                   0x08001671   Thumb Code  3942  main.o(i.auto_run_state_machine)
    degrees_to_steps                         0x08002719   Thumb Code    16  a4988.o(i.degrees_to_steps)
    gpio_init                                0x0800286d   Thumb Code    46  gpio.o(i.gpio_init)
    gpio_set_output                          0x080028a1   Thumb Code    34  gpio.o(i.gpio_set_output)
    i2c_init                                 0x08002911   Thumb Code    56  i2c.o(i.i2c_init)
    i2c_read_addr8_bytes                     0x0800297d   Thumb Code   138  i2c.o(i.i2c_read_addr8_bytes)
    i2c_read_addr8_data16                    0x08002a0d   Thumb Code    40  i2c.o(i.i2c_read_addr8_data16)
    i2c_read_addr8_data32                    0x08002a35   Thumb Code    56  i2c.o(i.i2c_read_addr8_data32)
    i2c_read_addr8_data8                     0x08002a6d   Thumb Code   110  i2c.o(i.i2c_read_addr8_data8)
    i2c_set_slave_address                    0x08002b95   Thumb Code     6  i2c.o(i.i2c_set_slave_address)
    i2c_write_addr8_bytes                    0x08002c51   Thumb Code   106  i2c.o(i.i2c_write_addr8_bytes)
    i2c_write_addr8_data8                    0x08002cc1   Thumb Code    88  i2c.o(i.i2c_write_addr8_data8)
    main                                     0x080030cd   Thumb Code  8360  main.o(i.main)
    motor2_smooth_pulses                     0x080054e9   Thumb Code   268  a4988.o(i.motor2_smooth_pulses)
    motor2_smooth_update                     0x08005601   Thumb Code   372  a4988.o(i.motor2_smooth_update)
    motors_get_sync_progress                 0x0800578d   Thumb Code   182  a4988.o(i.motors_get_sync_progress)
    motors_get_sync_speed_info               0x08005851   Thumb Code   162  a4988.o(i.motors_get_sync_speed_info)
    motors_start_sync_move                   0x08005901   Thumb Code   214  a4988.o(i.motors_start_sync_move)
    motors_sync_is_done                      0x080059e5   Thumb Code    30  a4988.o(i.motors_sync_is_done)
    motors_sync_update                       0x08005a11   Thumb Code   438  a4988.o(i.motors_sync_update)
    print_number                             0x08005c8b   Thumb Code    84  main.o(i.print_number)
    stepper1_software_tick                   0x08005e81   Thumb Code   174  a4988.o(i.stepper1_software_tick)
    stepper2_acceleration_tick               0x08005f45   Thumb Code   286  a4988.o(i.stepper2_acceleration_tick)
    storage_init                             0x08006079   Thumb Code     2  storage.o(i.storage_init)
    storage_read_offset                      0x0800607d   Thumb Code    20  storage.o(i.storage_read_offset)
    storage_write_offset                     0x08006095   Thumb Code    54  storage.o(i.storage_write_offset)
    usartGetReceivedByte                     0x080060d1   Thumb Code    44  usart.o(i.usartGetReceivedByte)
    usartInit                                0x08006105   Thumb Code   244  usart.o(i.usartInit)
    usartSendByte                            0x08006209   Thumb Code    54  usart.o(i.usartSendByte)
    usartSendData                            0x08006249   Thumb Code    52  usart.o(i.usartSendData)
    usartSendString                          0x08006281   Thumb Code    40  usart.o(i.usartSendString)
    vl53l0x_init                             0x080062ad   Thumb Code    28  vl53l0x.o(i.vl53l0x_init)
    vl53l0x_read_range_adaptive              0x080062c9   Thumb Code   238  vl53l0x.o(i.vl53l0x_read_range_adaptive)
    vl53l0x_read_range_average               0x080063b7   Thumb Code   104  vl53l0x.o(i.vl53l0x_read_range_average)
    vl53l0x_read_range_continuous            0x08006421   Thumb Code   176  vl53l0x.o(i.vl53l0x_read_range_continuous)
    vl53l0x_read_range_high_precision        0x080064e1   Thumb Code   236  vl53l0x.o(i.vl53l0x_read_range_high_precision)
    vl53l0x_read_range_single                0x080065cd   Thumb Code   236  vl53l0x.o(i.vl53l0x_read_range_single)
    __aeabi_fadd                             0x080066c1   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x080066c1   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcmp_Inf                           0x08006785   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x0800679d   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x0800679d   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2iz                             0x08006921   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x08006921   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_f2uiz                            0x08006959   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08006959   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x08006999   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08006999   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x080069c9   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x080069c9   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_cfcmple                          0x080069f1   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x080069f1   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x08006a43   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08006a59   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08006a59   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08006b5b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08006be7   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x08006bf1   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x08006bf1   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_fsub                             0x08006c55   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08006c55   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __I$use$fp                               0x08006d3e   Number         0  usenofp.o(x$fpl$usenofp)
    OLED_F8x16                               0x08006d40   Data        1520  oled_data.o(.constdata)
    OLED_F6x8                                0x08007330   Data         570  oled_data.o(.constdata)
    Region$$Table$$Base                      0x080075d4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080075f4   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    delay_state                              0x20000028   Data           4  delay.o(.data)
    stepper2_pulse_count                     0x20000034   Data           4  a4988.o(.data)
    stepper2_target_pulses                   0x20000038   Data           4  a4988.o(.data)
    stepper2_start_pulse                     0x2000003c   Data           4  a4988.o(.data)
    stepper2_busy                            0x20000040   Data           1  a4988.o(.data)
    stepper1_target_steps                    0x20000044   Data           4  a4988.o(.data)
    stepper1_start_step                      0x20000048   Data           4  a4988.o(.data)
    stepper1_busy                            0x2000004c   Data           1  a4988.o(.data)
    stepper1_state                           0x20000094   Data          20  a4988.o(.bss)
    stepper2_state                           0x200000a8   Data          20  a4988.o(.bss)
    motor2_smooth                            0x200000bc   Data          40  a4988.o(.bss)
    motor_sync                               0x200000e4   Data          24  a4988.o(.bss)
    OLED_DisplayBuf                          0x200000fc   Data        1024  oled.o(.bss)
    recorded_data                            0x200004fc   Data         160  main.o(.bss)
    __libspace_start                         0x2000059c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200005fc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007688, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000075f4, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         4247  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         4696    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         4698    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         4700    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000002   Code   RO         4556    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4569    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4571    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4574    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4576    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4578    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4581    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4583    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4585    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4587    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4589    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4591    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4593    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4595    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4597    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4599    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4601    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4605    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4607    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4609    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4611    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000002   Code   RO         4612    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000164   0x08000164   0x00000002   Code   RO         4636    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4649    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4651    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4654    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4657    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4659    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4662    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000002   Code   RO         4663    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000168   0x08000168   0x00000000   Code   RO         4321    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000168   0x08000168   0x00000000   Code   RO         4464    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000168   0x08000168   0x00000006   Code   RO         4476    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800016e   0x0800016e   0x00000000   Code   RO         4466    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800016e   0x0800016e   0x00000004   Code   RO         4467    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         4469    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000008   Code   RO         4470    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800017a   0x0800017a   0x00000002   Code   RO         4561    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         4616    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800017c   0x0800017c   0x00000004   Code   RO         4617    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000180   0x08000180   0x00000006   Code   RO         4618    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x00000040   Code   RO            4    .text               startup_stm32f10x_md.o
    0x080001c8   0x080001c8   0x0000004e   Code   RO         4243    .text               c_w.l(rt_memclr_w.o)
    0x08000216   0x08000216   0x00000006   Code   RO         4245    .text               c_w.l(heapauxi.o)
    0x0800021c   0x0800021c   0x00000008   Code   RO         4507    .text               c_w.l(libspace.o)
    0x08000224   0x08000224   0x0000004a   Code   RO         4510    .text               c_w.l(sys_stackheap_outer.o)
    0x0800026e   0x0800026e   0x00000012   Code   RO         4549    .text               c_w.l(exit.o)
    0x08000280   0x08000280   0x0000000c   Code   RO         4628    .text               c_w.l(sys_exit.o)
    0x0800028c   0x0800028c   0x00000002   Code   RO         4639    .text               c_w.l(use_no_semi.o)
    0x0800028e   0x0800028e   0x00000000   Code   RO         4641    .text               c_w.l(indicate_semi.o)
    0x0800028e   0x0800028e   0x00000004   Code   RO         4176    i.BusFault_Handler  stm32f10x_it.o
    0x08000292   0x08000292   0x00000002   Code   RO         4177    i.DebugMon_Handler  stm32f10x_it.o
    0x08000294   0x08000294   0x0000000c   Code   RO         3196    i.Delay_GetTick     delay.o
    0x080002a0   0x080002a0   0x00000054   Code   RO         3197    i.Delay_Init        delay.o
    0x080002f4   0x080002f4   0x00000018   Code   RO         3198    i.Delay_ms          delay.o
    0x0800030c   0x0800030c   0x0000001c   Code   RO         3200    i.Delay_us          delay.o
    0x08000328   0x08000328   0x0000004c   Code   RO         1046    i.FLASH_ErasePage   stm32f10x_flash.o
    0x08000374   0x08000374   0x00000034   Code   RO         1047    i.FLASH_GetBank1Status  stm32f10x_flash.o
    0x080003a8   0x080003a8   0x00000014   Code   RO         1056    i.FLASH_Lock        stm32f10x_flash.o
    0x080003bc   0x080003bc   0x00000040   Code   RO         1059    i.FLASH_ProgramHalfWord  stm32f10x_flash.o
    0x080003fc   0x080003fc   0x00000018   Code   RO         1064    i.FLASH_Unlock      stm32f10x_flash.o
    0x08000414   0x08000414   0x00000026   Code   RO         1068    i.FLASH_WaitForLastOperation  stm32f10x_flash.o
    0x0800043a   0x0800043a   0x00000116   Code   RO         1347    i.GPIO_Init         stm32f10x_gpio.o
    0x08000550   0x08000550   0x00000012   Code   RO         1351    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08000562   0x08000562   0x00000012   Code   RO         1353    i.GPIO_ReadOutputDataBit  stm32f10x_gpio.o
    0x08000574   0x08000574   0x00000004   Code   RO         1354    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000578   0x08000578   0x00000004   Code   RO         1355    i.GPIO_SetBits      stm32f10x_gpio.o
    0x0800057c   0x0800057c   0x0000000a   Code   RO         1358    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08000586   0x08000586   0x00000004   Code   RO         4178    i.HardFault_Handler  stm32f10x_it.o
    0x0800058a   0x0800058a   0x00000002   PAD
    0x0800058c   0x0800058c   0x0000005c   Code   RO         3257    i.Key_GetNum        key.o
    0x080005e8   0x080005e8   0x0000002c   Code   RO         3258    i.Key_Init          key.o
    0x08000614   0x08000614   0x00000004   Code   RO         4179    i.MemManage_Handler  stm32f10x_it.o
    0x08000618   0x08000618   0x00000002   Code   RO         4180    i.NMI_Handler       stm32f10x_it.o
    0x0800061a   0x0800061a   0x00000002   PAD
    0x0800061c   0x0800061c   0x00000070   Code   RO          137    i.NVIC_Init         misc.o
    0x0800068c   0x0800068c   0x00000028   Code   RO         3201    i.NVIC_SetPriority  delay.o
    0x080006b4   0x080006b4   0x00000028   Code   RO         3865    i.OLED_Clear        oled.o
    0x080006dc   0x080006dc   0x000000a8   Code   RO         3866    i.OLED_ClearArea    oled.o
    0x08000784   0x08000784   0x00000060   Code   RO         3874    i.OLED_GPIO_Init    oled.o
    0x080007e4   0x080007e4   0x0000003e   Code   RO         3876    i.OLED_I2C_SendByte  oled.o
    0x08000822   0x08000822   0x0000001c   Code   RO         3877    i.OLED_I2C_Start    oled.o
    0x0800083e   0x0800083e   0x00000016   Code   RO         3878    i.OLED_I2C_Stop     oled.o
    0x08000854   0x08000854   0x0000009a   Code   RO         3879    i.OLED_Init         oled.o
    0x080008ee   0x080008ee   0x00000014   Code   RO         3881    i.OLED_Pow          oled.o
    0x08000902   0x08000902   0x00000022   Code   RO         3885    i.OLED_SetCursor    oled.o
    0x08000924   0x08000924   0x00000054   Code   RO         3887    i.OLED_ShowChar     oled.o
    0x08000978   0x08000978   0x0000012c   Code   RO         3890    i.OLED_ShowImage    oled.o
    0x08000aa4   0x08000aa4   0x0000004c   Code   RO         3891    i.OLED_ShowNum      oled.o
    0x08000af0   0x08000af0   0x00000070   Code   RO         3892    i.OLED_ShowSignedNum  oled.o
    0x08000b60   0x08000b60   0x0000002e   Code   RO         3893    i.OLED_ShowString   oled.o
    0x08000b8e   0x08000b8e   0x00000002   PAD
    0x08000b90   0x08000b90   0x00000028   Code   RO         3894    i.OLED_Update       oled.o
    0x08000bb8   0x08000bb8   0x00000028   Code   RO         3896    i.OLED_W_SCL        oled.o
    0x08000be0   0x08000be0   0x00000028   Code   RO         3897    i.OLED_W_SDA        oled.o
    0x08000c08   0x08000c08   0x00000020   Code   RO         3898    i.OLED_WriteCommand  oled.o
    0x08000c28   0x08000c28   0x0000002e   Code   RO         3899    i.OLED_WriteData    oled.o
    0x08000c56   0x08000c56   0x00000002   Code   RO         4181    i.PendSV_Handler    stm32f10x_it.o
    0x08000c58   0x08000c58   0x00000020   Code   RO         1775    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000c78   0x08000c78   0x00000020   Code   RO         1777    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000c98   0x08000c98   0x000000d4   Code   RO         1785    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08000d6c   0x08000d6c   0x00000002   Code   RO         4182    i.SVC_Handler       stm32f10x_it.o
    0x08000d6e   0x08000d6e   0x00000008   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x08000d76   0x08000d76   0x00000002   PAD
    0x08000d78   0x08000d78   0x000000e0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x08000e58   0x08000e58   0x00000044   Code   RO         3202    i.SysTick_Handler   delay.o
    0x08000e9c   0x08000e9c   0x00000060   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x08000efc   0x08000efc   0x00000088   Code   RO         3279    i.TIM2_IRQHandler   a4988.o
    0x08000f84   0x08000f84   0x00000018   Code   RO         2409    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08000f9c   0x08000f9c   0x00000006   Code   RO         2416    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08000fa2   0x08000fa2   0x00000018   Code   RO         2421    i.TIM_Cmd           stm32f10x_tim.o
    0x08000fba   0x08000fba   0x0000001e   Code   RO         2423    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x08000fd8   0x08000fd8   0x00000004   Code   RO         2435    i.TIM_GenerateEvent  stm32f10x_tim.o
    0x08000fdc   0x08000fdc   0x00000022   Code   RO         2442    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08000ffe   0x08000ffe   0x00000012   Code   RO         2446    i.TIM_ITConfig      stm32f10x_tim.o
    0x08001010   0x08001010   0x000000a4   Code   RO         2455    i.TIM_OC2Init       stm32f10x_tim.o
    0x080010b4   0x080010b4   0x0000001a   Code   RO         2458    i.TIM_OC2PreloadConfig  stm32f10x_tim.o
    0x080010ce   0x080010ce   0x00000004   Code   RO         2480    i.TIM_SetAutoreload  stm32f10x_tim.o
    0x080010d2   0x080010d2   0x00000004   Code   RO         2483    i.TIM_SetCompare2   stm32f10x_tim.o
    0x080010d6   0x080010d6   0x00000002   PAD
    0x080010d8   0x080010d8   0x000000a4   Code   RO         2492    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x0800117c   0x0800117c   0x00000002   Code   RO         4183    i.USART1_IRQHandler  stm32f10x_it.o
    0x0800117e   0x0800117e   0x00000018   Code   RO         2960    i.USART_Cmd         stm32f10x_usart.o
    0x08001196   0x08001196   0x0000001a   Code   RO         2963    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x080011b0   0x080011b0   0x000000d8   Code   RO         2967    i.USART_Init        stm32f10x_usart.o
    0x08001288   0x08001288   0x0000000a   Code   RO         2974    i.USART_ReceiveData  stm32f10x_usart.o
    0x08001292   0x08001292   0x00000008   Code   RO         2977    i.USART_SendData    stm32f10x_usart.o
    0x0800129a   0x0800129a   0x00000004   Code   RO         4184    i.UsageFault_Handler  stm32f10x_it.o
    0x0800129e   0x0800129e   0x00000002   PAD
    0x080012a0   0x080012a0   0x00000080   Code   RO         3281    i._a4988_update_pwm  a4988.o
    0x08001320   0x08001320   0x00000060   Code   RO         3282    i.a4988_enable      a4988.o
    0x08001380   0x08001380   0x00000034   Code   RO         3283    i.a4988_get_angle   a4988.o
    0x080013b4   0x080013b4   0x00000134   Code   RO         3284    i.a4988_init        a4988.o
    0x080014e8   0x080014e8   0x00000024   Code   RO         3285    i.a4988_reset_angle  a4988.o
    0x0800150c   0x0800150c   0x00000048   Code   RO         3286    i.a4988_set_direction  a4988.o
    0x08001554   0x08001554   0x00000048   Code   RO         3287    i.a4988_set_speed1  a4988.o
    0x0800159c   0x0800159c   0x0000000c   Code   RO         3288    i.a4988_set_speed2  a4988.o
    0x080015a8   0x080015a8   0x0000000e   Code   RO         3289    i.a4988_start_move_steps1  a4988.o
    0x080015b6   0x080015b6   0x00000002   PAD
    0x080015b8   0x080015b8   0x00000054   Code   RO         3290    i.a4988_start_move_steps1_with_speed  a4988.o
    0x0800160c   0x0800160c   0x0000000e   Code   RO         3291    i.a4988_start_move_steps2  a4988.o
    0x0800161a   0x0800161a   0x00000002   PAD
    0x0800161c   0x0800161c   0x00000054   Code   RO         3292    i.a4988_start_move_steps2_with_speed  a4988.o
    0x08001670   0x08001670   0x00000fa0   Code   RO         4124    i.auto_run_state_machine  main.o
    0x08002610   0x08002610   0x00000010   Code   RO         3606    i.configure_address  vl53l0x.o
    0x08002620   0x08002620   0x00000020   Code   RO         3607    i.configure_gpio    vl53l0x.o
    0x08002640   0x08002640   0x0000004e   Code   RO         3608    i.configure_interrupt  vl53l0x.o
    0x0800268e   0x0800268e   0x00000002   PAD
    0x08002690   0x08002690   0x00000088   Code   RO         3609    i.data_init         vl53l0x.o
    0x08002718   0x08002718   0x00000010   Code   RO         3294    i.degrees_to_steps  a4988.o
    0x08002728   0x08002728   0x00000024   Code   RO         3610    i.device_is_booted  vl53l0x.o
    0x0800274c   0x0800274c   0x0000011e   Code   RO         3611    i.get_spad_info_from_nvm  vl53l0x.o
    0x0800286a   0x0800286a   0x00000002   PAD
    0x0800286c   0x0800286c   0x00000034   Code   RO         3483    i.gpio_init         gpio.o
    0x080028a0   0x080028a0   0x00000028   Code   RO         3484    i.gpio_set_output   gpio.o
    0x080028c8   0x080028c8   0x00000030   Code   RO         3507    i.i2c_ack           i2c.o
    0x080028f8   0x080028f8   0x00000018   Code   RO         3508    i.i2c_delay         i2c.o
    0x08002910   0x08002910   0x0000003c   Code   RO         3509    i.i2c_init          i2c.o
    0x0800294c   0x0800294c   0x00000030   Code   RO         3510    i.i2c_nack          i2c.o
    0x0800297c   0x0800297c   0x00000090   Code   RO         3511    i.i2c_read_addr8_bytes  i2c.o
    0x08002a0c   0x08002a0c   0x00000028   Code   RO         3512    i.i2c_read_addr8_data16  i2c.o
    0x08002a34   0x08002a34   0x00000038   Code   RO         3513    i.i2c_read_addr8_data32  i2c.o
    0x08002a6c   0x08002a6c   0x00000074   Code   RO         3514    i.i2c_read_addr8_data8  i2c.o
    0x08002ae0   0x08002ae0   0x00000060   Code   RO         3515    i.i2c_read_byte     i2c.o
    0x08002b40   0x08002b40   0x00000054   Code   RO         3516    i.i2c_send_byte     i2c.o
    0x08002b94   0x08002b94   0x0000000c   Code   RO         3517    i.i2c_set_slave_address  i2c.o
    0x08002ba0   0x08002ba0   0x00000034   Code   RO         3518    i.i2c_start         i2c.o
    0x08002bd4   0x08002bd4   0x00000034   Code   RO         3519    i.i2c_stop          i2c.o
    0x08002c08   0x08002c08   0x00000048   Code   RO         3520    i.i2c_wait_ack      i2c.o
    0x08002c50   0x08002c50   0x00000070   Code   RO         3521    i.i2c_write_addr8_bytes  i2c.o
    0x08002cc0   0x08002cc0   0x0000005c   Code   RO         3522    i.i2c_write_addr8_data8  i2c.o
    0x08002d1c   0x08002d1c   0x0000003c   Code   RO         3612    i.init_address      vl53l0x.o
    0x08002d58   0x08002d58   0x00000016   Code   RO         3613    i.init_addresses    vl53l0x.o
    0x08002d6e   0x08002d6e   0x00000002   PAD
    0x08002d70   0x08002d70   0x00000034   Code   RO         3614    i.init_config       vl53l0x.o
    0x08002da4   0x08002da4   0x00000326   Code   RO         3615    i.load_default_tuning_settings  vl53l0x.o
    0x080030ca   0x080030ca   0x00000002   PAD
    0x080030cc   0x080030cc   0x0000241c   Code   RO         4125    i.main              main.o
    0x080054e8   0x080054e8   0x00000118   Code   RO         3302    i.motor2_smooth_pulses  a4988.o
    0x08005600   0x08005600   0x0000018c   Code   RO         3303    i.motor2_smooth_update  a4988.o
    0x0800578c   0x0800578c   0x000000c4   Code   RO         3304    i.motors_get_sync_progress  a4988.o
    0x08005850   0x08005850   0x000000b0   Code   RO         3305    i.motors_get_sync_speed_info  a4988.o
    0x08005900   0x08005900   0x000000e4   Code   RO         3306    i.motors_start_sync_move  a4988.o
    0x080059e4   0x080059e4   0x0000002c   Code   RO         3307    i.motors_sync_is_done  a4988.o
    0x08005a10   0x08005a10   0x000001d0   Code   RO         3308    i.motors_sync_update  a4988.o
    0x08005be0   0x08005be0   0x0000002a   Code   RO         3616    i.perform_ref_calibration  vl53l0x.o
    0x08005c0a   0x08005c0a   0x00000080   Code   RO         3617    i.perform_single_ref_calibration  vl53l0x.o
    0x08005c8a   0x08005c8a   0x00000054   Code   RO         4126    i.print_number      main.o
    0x08005cde   0x08005cde   0x00000044   Code   RO         3618    i.read_strobe       vl53l0x.o
    0x08005d22   0x08005d22   0x00000002   PAD
    0x08005d24   0x08005d24   0x0000001c   Code   RO         3619    i.set_hardware_standby  vl53l0x.o
    0x08005d40   0x08005d40   0x0000000e   Code   RO         3620    i.set_sequence_steps_enabled  vl53l0x.o
    0x08005d4e   0x08005d4e   0x00000102   Code   RO         3621    i.set_spads_from_nvm  vl53l0x.o
    0x08005e50   0x08005e50   0x00000030   Code   RO         3622    i.static_init       vl53l0x.o
    0x08005e80   0x08005e80   0x000000c4   Code   RO         3309    i.stepper1_software_tick  a4988.o
    0x08005f44   0x08005f44   0x00000134   Code   RO         3310    i.stepper2_acceleration_tick  a4988.o
    0x08006078   0x08006078   0x00000002   Code   RO         3838    i.storage_init      storage.o
    0x0800607a   0x0800607a   0x00000002   PAD
    0x0800607c   0x0800607c   0x00000018   Code   RO         3839    i.storage_read_offset  storage.o
    0x08006094   0x08006094   0x0000003c   Code   RO         3840    i.storage_write_offset  storage.o
    0x080060d0   0x080060d0   0x00000034   Code   RO         3751    i.usartGetReceivedByte  usart.o
    0x08006104   0x08006104   0x00000104   Code   RO         3753    i.usartInit         usart.o
    0x08006208   0x08006208   0x00000040   Code   RO         3758    i.usartSendByte     usart.o
    0x08006248   0x08006248   0x00000038   Code   RO         3759    i.usartSendData     usart.o
    0x08006280   0x08006280   0x0000002c   Code   RO         3760    i.usartSendString   usart.o
    0x080062ac   0x080062ac   0x0000001c   Code   RO         3623    i.vl53l0x_init      vl53l0x.o
    0x080062c8   0x080062c8   0x000000ee   Code   RO         3624    i.vl53l0x_read_range_adaptive  vl53l0x.o
    0x080063b6   0x080063b6   0x00000068   Code   RO         3625    i.vl53l0x_read_range_average  vl53l0x.o
    0x0800641e   0x0800641e   0x00000002   PAD
    0x08006420   0x08006420   0x000000c0   Code   RO         3626    i.vl53l0x_read_range_continuous  vl53l0x.o
    0x080064e0   0x080064e0   0x000000ec   Code   RO         3627    i.vl53l0x_read_range_high_precision  vl53l0x.o
    0x080065cc   0x080065cc   0x000000f4   Code   RO         3628    i.vl53l0x_read_range_single  vl53l0x.o
    0x080066c0   0x080066c0   0x000000c4   Code   RO         4281    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08006784   0x08006784   0x00000018   Code   RO         4423    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x0800679c   0x0800679c   0x00000184   Code   RO         4288    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08006920   0x08006920   0x00000036   Code   RO         4291    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08006956   0x08006956   0x00000002   PAD
    0x08006958   0x08006958   0x0000003e   Code   RO         4295    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x08006996   0x08006996   0x00000002   PAD
    0x08006998   0x08006998   0x00000030   Code   RO         4300    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x080069c8   0x080069c8   0x00000026   Code   RO         4299    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x080069ee   0x080069ee   0x00000002   PAD
    0x080069f0   0x080069f0   0x00000068   Code   RO         4305    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08006a58   0x08006a58   0x00000102   Code   RO         4307    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08006b5a   0x08006b5a   0x0000008c   Code   RO         4425    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08006be6   0x08006be6   0x0000000a   Code   RO         4427    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08006bf0   0x08006bf0   0x00000062   Code   RO         4309    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x08006c52   0x08006c52   0x00000002   PAD
    0x08006c54   0x08006c54   0x000000ea   Code   RO         4283    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08006d3e   0x08006d3e   0x00000000   Code   RO         4437    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08006d3e   0x08006d3e   0x00000002   Data   RO         3630    .constdata          vl53l0x.o
    0x08006d40   0x08006d40   0x0000082a   Data   RO         4111    .constdata          oled_data.o
    0x0800756a   0x0800756a   0x00000002   PAD
    0x0800756c   0x0800756c   0x0000000c   Data   RO         4128    .constdata          main.o
    0x08007578   0x08007578   0x0000005c   Data   RO         4129    .conststring        main.o
    0x080075d4   0x080075d4   0x00000020   Data   RO         4694    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080075f4, Size: 0x00000c00, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080075f4   0x00000014   Data   RW           28    .data               system_stm32f10x.o
    0x20000014   0x08007608   0x00000014   Data   RW         1805    .data               stm32f10x_rcc.o
    0x20000028   0x0800761c   0x00000009   Data   RW         3203    .data               delay.o
    0x20000031   0x08007625   0x00000003   PAD
    0x20000034   0x08007628   0x00000024   Data   RW         3312    .data               a4988.o
    0x20000058   0x0800764c   0x00000001   Data   RW         3523    .data               i2c.o
    0x20000059   0x0800764d   0x00000001   PAD
    0x2000005a   0x0800764e   0x00000005   Data   RW         3631    .data               vl53l0x.o
    0x2000005f   0x08007653   0x00000001   PAD
    0x20000060   0x08007654   0x00000006   Data   RW         3762    .data               usart.o
    0x20000066   0x0800765a   0x00000002   PAD
    0x20000068   0x0800765c   0x0000002c   Data   RW         4130    .data               main.o
    0x20000094        -       0x00000068   Zero   RW         3311    .bss                a4988.o
    0x200000fc        -       0x00000400   Zero   RW         3901    .bss                oled.o
    0x200004fc        -       0x000000a0   Zero   RW         4127    .bss                main.o
    0x2000059c        -       0x00000060   Zero   RW         4508    .bss                c_w.l(libspace.o)
    0x200005fc   0x08007688   0x00000004   PAD
    0x20000600        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f10x_md.o
    0x20000800        -       0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      3412        296          0         36        104      17500   a4988.o
         0          0          0          0          0       4500   core_cm3.o
       256         42          0          9          0      18542   delay.o
        92         12          0          0          0       1402   gpio.o
      1108         62          0          1          0       9053   i2c.o
       136          8          0          0          0        932   key.o
     13328       5778        104         44        160      11895   main.o
       112         12          0          0          0     204184   misc.o
      1440         40          0          0       1024      11350   oled.o
         0          0       2090          0          0        535   oled_data.o
        64         26        236          0       1536        800   startup_stm32f10x_md.o
         0          0          0          0          0       1636   stm32f10x_adc.o
       274         30          0          0          0       9382   stm32f10x_flash.o
       332          0          0          0          0      12753   stm32f10x_gpio.o
        26          0          0          0          0       3552   stm32f10x_it.o
       276         32          0         20          0      12850   stm32f10x_rcc.o
       502         52          0          0          0      27141   stm32f10x_tim.o
       284          6          0          0          0      10200   stm32f10x_usart.o
        86         10          0          0          0       1500   storage.o
       328         28          0         20          0      51601   system_stm32f10x.o
       476         42          0          6          0       4429   usart.o
      3152         44          2          5          0      18298   vl53l0x.o

    ----------------------------------------------------------------------
     25714       <USER>       <GROUP>        148       2824     434035   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        30          0          2          7          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
       430          8          0          0          0        168   faddsub_clz.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
        54          4          0          0          0         84   ffix.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      2016        <USER>          <GROUP>          0        100       1688   Library Totals
        12          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       350         16          0          0         96        664   c_w.l
      1654        104          0          0          0       1024   fz_ws.l

    ----------------------------------------------------------------------
      2016        <USER>          <GROUP>          0        100       1688   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     27730       6640       2466        148       2924     427451   Grand Totals
     27730       6640       2466        148       2924     427451   ELF Image Totals
     27730       6640       2466        148          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                30196 (  29.49kB)
    Total RW  Size (RW Data + ZI Data)              3072 (   3.00kB)
    Total ROM Size (Code + RO Data + RW Data)      30344 (  29.63kB)

==============================================================================

