<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\Project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\Project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060528: Last Updated: Mon Jul 28 11:55:32 2025
<BR><P>
<H3>Maximum Stack Usage =        792 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; auto_run_state_machine &rArr; motors_start_sync_move &rArr; motor2_smooth_pulses &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[4c]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[20]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[20]">ADC1_2_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[20]">ADC1_2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[8]">BusFault_Handler</a> from stm32f10x_it.o(i.BusFault_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[23]">CAN1_RX1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[24]">CAN1_SCE_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[19]">DMA1_Channel1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel4_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel5_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel6_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1f]">DMA1_Channel7_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[b]">DebugMon_Handler</a> from stm32f10x_it.o(i.DebugMon_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[14]">EXTI0_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[36]">EXTI15_10_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[15]">EXTI1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[16]">EXTI2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[17]">EXTI3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[18]">EXTI4_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[25]">EXTI9_5_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[12]">FLASH_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[6]">HardFault_Handler</a> from stm32f10x_it.o(i.HardFault_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[30]">I2C2_ER_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2f]">I2C2_EV_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[7]">MemManage_Handler</a> from stm32f10x_it.o(i.MemManage_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[5]">NMI_Handler</a> from stm32f10x_it.o(i.NMI_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[f]">PVD_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[c]">PendSV_Handler</a> from stm32f10x_it.o(i.PendSV_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[13]">RCC_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[37]">RTCAlarm_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[11]">RTC_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[4]">Reset_Handler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[31]">SPI1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[32]">SPI2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[a]">SVC_Handler</a> from stm32f10x_it.o(i.SVC_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[d]">SysTick_Handler</a> from delay.o(i.SysTick_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[39]">SystemInit</a> from system_stm32f10x.o(i.SystemInit) referenced from startup_stm32f10x_md.o(.text)
 <LI><a href="#[10]">TAMPER_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[26]">TIM1_BRK_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[29]">TIM1_CC_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[28]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[27]">TIM1_UP_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2a]">TIM2_IRQHandler</a> from a4988.o(i.TIM2_IRQHandler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2b]">TIM3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2c]">TIM4_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[33]">USART1_IRQHandler</a> from stm32f10x_it.o(i.USART1_IRQHandler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[34]">USART2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[35]">USART3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[38]">USBWakeUp_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[21]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[22]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[9]">UsageFault_Handler</a> from stm32f10x_it.o(i.UsageFault_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[e]">WWDG_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[3b]">__main</a> from __main.o(!!!main) referenced from startup_stm32f10x_md.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[3b]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[3c]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[3e]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[ec]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[ed]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[3f]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[ee]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[43]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[ef]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[f0]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[f1]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[f2]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[f3]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[f4]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[f5]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[f6]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[f7]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[f8]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[f9]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[fa]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[fb]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[fc]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[fd]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[fe]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[ff]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[100]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[101]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[102]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[103]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[48]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[104]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[105]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[106]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[107]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[108]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[109]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[10a]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[3d]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[10b]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[40]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[42]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[10c]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[44]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 792 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; auto_run_state_machine &rArr; motors_start_sync_move &rArr; motor2_smooth_pulses &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10d]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[4d]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[47]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[10e]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[49]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[4]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f10x_md.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[c9]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10f]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[110]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[111]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[112]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[113]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[114]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[115]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[4b]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[116]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[41]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[46]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[4a]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[117]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[118]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[8]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[119]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[b]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[cf]"></a>Delay_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, delay.o(i.Delay_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_start_sync_move
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
</UL>

<P><STRONG><a name="[4e]"></a>Delay_Init</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, delay.o(i.Delay_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Delay_Init &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[50]"></a>Delay_ms</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, delay.o(i.Delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_high_precision
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_average
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_adaptive
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_address
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_GetNum
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[51]"></a>Delay_us</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, delay.o(i.Delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>

<P><STRONG><a name="[52]"></a>FLASH_ErasePage</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, stm32f10x_flash.o(i.FLASH_ErasePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_ErasePage &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;storage_write_offset
</UL>

<P><STRONG><a name="[55]"></a>FLASH_GetBank1Status</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_GetBank1Status))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>

<P><STRONG><a name="[d7]"></a>FLASH_Lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_Lock))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;storage_write_offset
</UL>

<P><STRONG><a name="[54]"></a>FLASH_ProgramHalfWord</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f10x_flash.o(i.FLASH_ProgramHalfWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = FLASH_ProgramHalfWord &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;storage_write_offset
</UL>

<P><STRONG><a name="[d6]"></a>FLASH_Unlock</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f10x_flash.o(i.FLASH_Unlock))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;storage_write_offset
</UL>

<P><STRONG><a name="[53]"></a>FLASH_WaitForLastOperation</STRONG> (Thumb, 38 bytes, Stack size 4 bytes, stm32f10x_flash.o(i.FLASH_WaitForLastOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_GetBank1Status
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramHalfWord
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ErasePage
</UL>

<P><STRONG><a name="[5a]"></a>GPIO_Init</STRONG> (Thumb, 278 bytes, Stack size 24 bytes, stm32f10x_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartInit
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
</UL>

<P><STRONG><a name="[57]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_ReadInputDataBit))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_GetNum
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c6]"></a>GPIO_ReadOutputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[82]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_ResetBits))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_nack
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_ack
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_set_output
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper1_software_tick
</UL>

<P><STRONG><a name="[83]"></a>GPIO_SetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_SetBits))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_nack
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_ack
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_set_output
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper1_software_tick
</UL>

<P><STRONG><a name="[6e]"></a>GPIO_WriteBit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_WriteBit))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_direction
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
</UL>

<P><STRONG><a name="[6]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>Key_GetNum</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, key.o(i.Key_GetNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Key_GetNum &rArr; Delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[58]"></a>Key_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, key.o(i.Key_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Key_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>NVIC_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, misc.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartInit
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
</UL>

<P><STRONG><a name="[63]"></a>OLED_Clear</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[68]"></a>OLED_ClearArea</STRONG> (Thumb, 162 bytes, Stack size 28 bytes, oled.o(i.OLED_ClearArea))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = OLED_ClearArea
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
</UL>

<P><STRONG><a name="[5b]"></a>OLED_GPIO_Init</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, oled.o(i.OLED_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = OLED_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[5e]"></a>OLED_I2C_SendByte</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, oled.o(i.OLED_I2C_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[5f]"></a>OLED_I2C_Start</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OLED_I2C_Start &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[60]"></a>OLED_I2C_Stop</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OLED_I2C_Stop &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[61]"></a>OLED_Init</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OLED_Init &rArr; OLED_Update &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6a]"></a>OLED_Pow</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, oled.o(i.OLED_Pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_Pow
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowSignedNum
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
</UL>

<P><STRONG><a name="[65]"></a>OLED_SetCursor</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, oled.o(i.OLED_SetCursor))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
</UL>

<P><STRONG><a name="[66]"></a>OLED_ShowChar</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowSignedNum
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
</UL>

<P><STRONG><a name="[67]"></a>OLED_ShowImage</STRONG> (Thumb, 296 bytes, Stack size 32 bytes, oled.o(i.OLED_ShowImage))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ClearArea
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[69]"></a>OLED_ShowNum</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, oled.o(i.OLED_ShowNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Pow
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[6b]"></a>OLED_ShowSignedNum</STRONG> (Thumb, 112 bytes, Stack size 36 bytes, oled.o(i.OLED_ShowSignedNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = OLED_ShowSignedNum &rArr; OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Pow
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[6c]"></a>OLED_ShowString</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[64]"></a>OLED_Update</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, oled.o(i.OLED_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OLED_Update &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[5c]"></a>OLED_W_SCL</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, oled.o(i.OLED_W_SCL))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_W_SCL
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
</UL>

<P><STRONG><a name="[5d]"></a>OLED_W_SDA</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, oled.o(i.OLED_W_SDA))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
</UL>

<P><STRONG><a name="[62]"></a>OLED_WriteCommand</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, oled.o(i.OLED_WriteCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = OLED_WriteCommand &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[6d]"></a>OLED_WriteData</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, oled.o(i.OLED_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OLED_WriteData &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
</UL>

<P><STRONG><a name="[c]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
</UL>

<P><STRONG><a name="[59]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartInit
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
</UL>

<P><STRONG><a name="[77]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 192 bytes, Stack size 12 bytes, stm32f10x_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[a]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>SysTick_Handler</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, delay.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SysTick_Handler &rArr; stepper1_software_tick &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper1_software_tick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>SystemInit</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, system_stm32f10x.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SystemInit &rArr; SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(.text)
</UL>
<P><STRONG><a name="[2a]"></a>TIM2_IRQHandler</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, a4988.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = TIM2_IRQHandler &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>TIM_ARRPreloadConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ARRPreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
</UL>

<P><STRONG><a name="[73]"></a>TIM_ClearITPendingBit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ClearITPendingBit))
<BR><BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[74]"></a>TIM_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[89]"></a>TIM_CtrlPWMOutputs</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_CtrlPWMOutputs))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
</UL>

<P><STRONG><a name="[7e]"></a>TIM_GenerateEvent</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_GenerateEvent))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_a4988_update_pwm
</UL>

<P><STRONG><a name="[72]"></a>TIM_GetITStatus</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, stm32f10x_tim.o(i.TIM_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[88]"></a>TIM_ITConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ITConfig))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
</UL>

<P><STRONG><a name="[85]"></a>TIM_OC2Init</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, stm32f10x_tim.o(i.TIM_OC2Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC2Init
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
</UL>

<P><STRONG><a name="[86]"></a>TIM_OC2PreloadConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_OC2PreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
</UL>

<P><STRONG><a name="[7d]"></a>TIM_SetAutoreload</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_SetAutoreload))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_a4988_update_pwm
</UL>

<P><STRONG><a name="[79]"></a>TIM_SetCompare2</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_SetCompare2))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_a4988_update_pwm
</UL>

<P><STRONG><a name="[84]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_TimeBaseInit))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
</UL>

<P><STRONG><a name="[33]"></a>USART1_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.USART1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[da]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartInit
</UL>

<P><STRONG><a name="[d8]"></a>USART_GetFlagStatus</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_GetFlagStatus))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendByte
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartGetReceivedByte
</UL>

<P><STRONG><a name="[76]"></a>USART_Init</STRONG> (Thumb, 210 bytes, Stack size 56 bytes, stm32f10x_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartInit
</UL>

<P><STRONG><a name="[d9]"></a>USART_ReceiveData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartGetReceivedByte
</UL>

<P><STRONG><a name="[db]"></a>USART_SendData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendByte
</UL>

<P><STRONG><a name="[9]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>a4988_enable</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, a4988.o(i.a4988_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed2
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_a4988_update_pwm
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper2_acceleration_tick
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps2_with_speed
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps1_with_speed
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper1_software_tick
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[93]"></a>a4988_get_angle</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, a4988.o(i.a4988_get_angle))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[80]"></a>a4988_init</STRONG> (Thumb, 294 bytes, Stack size 40 bytes, a4988.o(i.a4988_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = a4988_init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PreloadConfig
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2Init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[94]"></a>a4988_reset_angle</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, a4988.o(i.a4988_reset_angle))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[8b]"></a>a4988_set_direction</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, a4988.o(i.a4988_set_direction))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = a4988_set_direction
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_start_sync_move
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps2_with_speed
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps1_with_speed
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8c]"></a>a4988_set_speed1</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, a4988.o(i.a4988_set_speed1))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = a4988_set_speed1 &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps1_with_speed
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper1_software_tick
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[7f]"></a>a4988_set_speed2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, a4988.o(i.a4988_set_speed2))
<BR><BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper2_acceleration_tick
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps2_with_speed
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8d]"></a>a4988_start_move_steps1</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, a4988.o(i.a4988_start_move_steps1))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = a4988_start_move_steps1 &rArr; a4988_start_move_steps1_with_speed &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps1_with_speed
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[8e]"></a>a4988_start_move_steps1_with_speed</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, a4988.o(i.a4988_start_move_steps1_with_speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = a4988_start_move_steps1_with_speed &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed1
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_direction
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_start_sync_move
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps1
</UL>

<P><STRONG><a name="[8f]"></a>a4988_start_move_steps2</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, a4988.o(i.a4988_start_move_steps2))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = a4988_start_move_steps2 &rArr; a4988_start_move_steps2_with_speed &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps2_with_speed
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[90]"></a>a4988_start_move_steps2_with_speed</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, a4988.o(i.a4988_start_move_steps2_with_speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = a4988_start_move_steps2_with_speed &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed2
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_direction
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps2
</UL>

<P><STRONG><a name="[91]"></a>auto_run_state_machine</STRONG> (Thumb, 3942 bytes, Stack size 64 bytes, main.o(i.auto_run_state_machine))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = auto_run_state_machine &rArr; motors_start_sync_move &rArr; motor2_smooth_pulses &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowSignedNum
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendString
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper2_acceleration_tick
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_is_done
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_start_sync_move
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_speed_info
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_progress
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;degrees_to_steps
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps2
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps1
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed1
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_reset_angle
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_get_angle
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper1_software_tick
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[99]"></a>degrees_to_steps</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, a4988.o(i.degrees_to_steps))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[a2]"></a>gpio_init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gpio.o(i.gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = gpio_init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;configure_gpio
</UL>

<P><STRONG><a name="[a3]"></a>gpio_set_output</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, gpio.o(i.gpio_set_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = gpio_set_output
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_hardware_standby
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;configure_gpio
</UL>

<P><STRONG><a name="[ae]"></a>i2c_init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, i2c.o(i.i2c_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = i2c_init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ab]"></a>i2c_read_addr8_bytes</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, i2c.o(i.i2c_read_addr8_bytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = i2c_read_addr8_bytes &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_spad_info_from_nvm
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data32
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data16
</UL>

<P><STRONG><a name="[b5]"></a>i2c_read_addr8_data16</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, i2c.o(i.i2c_read_addr8_data16))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = i2c_read_addr8_data16 &rArr; i2c_read_addr8_bytes &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_bytes
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
</UL>

<P><STRONG><a name="[aa]"></a>i2c_read_addr8_data32</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, i2c.o(i.i2c_read_addr8_data32))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = i2c_read_addr8_data32 &rArr; i2c_read_addr8_bytes &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_bytes
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_spad_info_from_nvm
</UL>

<P><STRONG><a name="[a5]"></a>i2c_read_addr8_data8</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, i2c.o(i.i2c_read_addr8_data8))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_strobe
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_single_ref_calibration
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_spad_info_from_nvm
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_is_booted
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;configure_interrupt
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b9]"></a>i2c_set_slave_address</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(i.i2c_set_slave_address))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_config
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_address
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b6]"></a>i2c_write_addr8_bytes</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, i2c.o(i.i2c_write_addr8_bytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = i2c_write_addr8_bytes &rArr; i2c_send_byte &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_spads_from_nvm
</UL>

<P><STRONG><a name="[a0]"></a>i2c_write_addr8_data8</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, i2c.o(i.i2c_write_addr8_data8))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = i2c_write_addr8_data8 &rArr; i2c_send_byte &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_spads_from_nvm
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_steps_enabled
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_strobe
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_single_ref_calibration
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_default_tuning_settings
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_spad_info_from_nvm
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;configure_interrupt
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;configure_address
</UL>

<P><STRONG><a name="[45]"></a>main</STRONG> (Thumb, 8360 bytes, Stack size 592 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 792<LI>Call Chain = main &rArr; auto_run_state_machine &rArr; motors_start_sync_move &rArr; motor2_smooth_pulses &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadOutputDataBit
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowSignedNum
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;storage_write_offset
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;storage_read_offset
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;storage_init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendString
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendByte
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartInit
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartGetReceivedByte
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_high_precision
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_continuous
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_average
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_adaptive
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_set_slave_address
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed2
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_direction
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_reset_angle
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_init
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_get_angle
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_GetNum
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper1_software_tick
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[cd]"></a>motor2_smooth_pulses</STRONG> (Thumb, 268 bytes, Stack size 40 bytes, a4988.o(i.motor2_smooth_pulses))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = motor2_smooth_pulses &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_GetTick
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare2
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetAutoreload
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_start_sync_move
</UL>

<P><STRONG><a name="[d0]"></a>motor2_smooth_update</STRONG> (Thumb, 372 bytes, Stack size 48 bytes, a4988.o(i.motor2_smooth_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = motor2_smooth_update &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_GetTick
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare2
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetAutoreload
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
</UL>

<P><STRONG><a name="[9b]"></a>motors_get_sync_progress</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, a4988.o(i.motors_get_sync_progress))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = motors_get_sync_progress &rArr; __aeabi_cfcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[9e]"></a>motors_get_sync_speed_info</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, a4988.o(i.motors_get_sync_speed_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = motors_get_sync_speed_info &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[96]"></a>motors_start_sync_move</STRONG> (Thumb, 214 bytes, Stack size 32 bytes, a4988.o(i.motors_start_sync_move))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = motors_start_sync_move &rArr; motor2_smooth_pulses &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_start_move_steps1_with_speed
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_direction
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[98]"></a>motors_sync_is_done</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, a4988.o(i.motors_sync_is_done))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[c4]"></a>motors_sync_update</STRONG> (Thumb, 438 bytes, Stack size 40 bytes, a4988.o(i.motors_sync_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = motors_sync_update &rArr; motor2_smooth_update &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_GetTick
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare2
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetAutoreload
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[97]"></a>print_number</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, main.o(i.print_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = print_number &rArr; usartSendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[71]"></a>stepper1_software_tick</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, a4988.o(i.stepper1_software_tick))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = stepper1_software_tick &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed1
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[9a]"></a>stepper2_acceleration_tick</STRONG> (Thumb, 286 bytes, Stack size 16 bytes, a4988.o(i.stepper2_acceleration_tick))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = stepper2_acceleration_tick &rArr; a4988_enable &rArr; _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed2
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_a4988_update_pwm
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[bf]"></a>storage_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, storage.o(i.storage_init))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c3]"></a>storage_read_offset</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, storage.o(i.storage_read_offset))
<BR><BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c8]"></a>storage_write_offset</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, storage.o(i.storage_write_offset))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = storage_write_offset &rArr; FLASH_ProgramHalfWord &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Unlock
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ProgramHalfWord
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Lock
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_ErasePage
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c5]"></a>usartGetReceivedByte</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, usart.o(i.usartGetReceivedByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usartGetReceivedByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c0]"></a>usartInit</STRONG> (Thumb, 244 bytes, Stack size 32 bytes, usart.o(i.usartInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = usartInit &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c1]"></a>usartSendByte</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, usart.o(i.usartSendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usartSendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendData
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[dc]"></a>usartSendData</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, usart.o(i.usartSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usartSendData &rArr; usartSendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendString
</UL>

<P><STRONG><a name="[95]"></a>usartSendString</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, usart.o(i.usartSendString))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usartSendString &rArr; usartSendData &rArr; usartSendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendData
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[c2]"></a>vl53l0x_init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, vl53l0x.o(i.vl53l0x_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = vl53l0x_init &rArr; init_config &rArr; static_init &rArr; set_spads_from_nvm &rArr; get_spad_info_from_nvm &rArr; read_strobe &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_config
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_addresses
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cb]"></a>vl53l0x_read_range_adaptive</STRONG> (Thumb, 238 bytes, Stack size 96 bytes, vl53l0x.o(i.vl53l0x_read_range_adaptive))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = vl53l0x_read_range_adaptive &rArr; vl53l0x_read_range_single &rArr; i2c_read_addr8_data16 &rArr; i2c_read_addr8_bytes &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c7]"></a>vl53l0x_read_range_average</STRONG> (Thumb, 104 bytes, Stack size 32 bytes, vl53l0x.o(i.vl53l0x_read_range_average))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = vl53l0x_read_range_average &rArr; vl53l0x_read_range_single &rArr; i2c_read_addr8_data16 &rArr; i2c_read_addr8_bytes &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cc]"></a>vl53l0x_read_range_continuous</STRONG> (Thumb, 176 bytes, Stack size 48 bytes, vl53l0x.o(i.vl53l0x_read_range_continuous))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = vl53l0x_read_range_continuous &rArr; vl53l0x_read_range_single &rArr; i2c_read_addr8_data16 &rArr; i2c_read_addr8_bytes &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ca]"></a>vl53l0x_read_range_high_precision</STRONG> (Thumb, 236 bytes, Stack size 96 bytes, vl53l0x.o(i.vl53l0x_read_range_high_precision))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = vl53l0x_read_range_high_precision &rArr; vl53l0x_read_range_single &rArr; i2c_read_addr8_data16 &rArr; i2c_read_addr8_bytes &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_single
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[92]"></a>vl53l0x_read_range_single</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, vl53l0x.o(i.vl53l0x_read_range_single))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = vl53l0x_read_range_single &rArr; i2c_read_addr8_data16 &rArr; i2c_read_addr8_bytes &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_set_slave_address
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data16
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_high_precision
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_continuous
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_average
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_adaptive
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[dd]"></a>__aeabi_fadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_continuous
</UL>

<P><STRONG><a name="[de]"></a>_fadd</STRONG> (Thumb, 196 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
</UL>

<P><STRONG><a name="[e6]"></a>__fpl_fcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, fcmpi.o(x$fpl$fcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
</UL>

<P><STRONG><a name="[7b]"></a>__aeabi_fdiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_speed_info
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_progress
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed1
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_a4988_update_pwm
</UL>

<P><STRONG><a name="[e2]"></a>_fdiv</STRONG> (Thumb, 384 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[9d]"></a>__aeabi_f2iz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[e3]"></a>_ffix</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[7c]"></a>__aeabi_f2uiz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_continuous
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_speed_info
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed1
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_a4988_update_pwm
</UL>

<P><STRONG><a name="[e4]"></a>_ffixu</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[7a]"></a>__aeabi_i2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$fflt))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_speed_info
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_progress
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_set_speed1
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_a4988_update_pwm
</UL>

<P><STRONG><a name="[11a]"></a>_fflt</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$fflt), UNUSED)

<P><STRONG><a name="[ce]"></a>__aeabi_ui2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_continuous
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_speed_info
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_progress
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
</UL>

<P><STRONG><a name="[11b]"></a>_ffltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu), UNUSED)

<P><STRONG><a name="[d1]"></a>__aeabi_cfcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fleqf.o(x$fpl$fleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cfcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_speed_info
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_progress
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
</UL>

<P><STRONG><a name="[e5]"></a>_fcmple</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, fleqf.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_Inf
</UL>

<P><STRONG><a name="[e9]"></a>__fpl_fcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fleqf.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frcmple
</UL>

<P><STRONG><a name="[9c]"></a>__aeabi_fmul</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_read_range_continuous
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_speed_info
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_pulses
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_run_state_machine
</UL>

<P><STRONG><a name="[e7]"></a>_fmul</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[e1]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffixu
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffix
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>

<P><STRONG><a name="[e0]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>

<P><STRONG><a name="[d2]"></a>__aeabi_cfrcmple</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, frleqf.o(x$fpl$frleqf))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_sync_update
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_speed_info
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motors_get_sync_progress
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor2_smooth_update
</UL>

<P><STRONG><a name="[e8]"></a>_frcmple</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, frleqf.o(x$fpl$frleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmple_InfNaN
</UL>

<P><STRONG><a name="[11c]"></a>__aeabi_fsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)

<P><STRONG><a name="[ea]"></a>_fsub</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[6f]"></a>SetSysClock</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_stm32f10x.o(i.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[70]"></a>SetSysClockTo72</STRONG> (Thumb, 214 bytes, Stack size 12 bytes, system_stm32f10x.o(i.SetSysClockTo72))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>

<P><STRONG><a name="[4f]"></a>NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, delay.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Init
</UL>

<P><STRONG><a name="[78]"></a>_a4988_update_pwm</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, a4988.o(i._a4988_update_pwm))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _a4988_update_pwm &rArr; __aeabi_f2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare2
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetAutoreload
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GenerateEvent
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stepper2_acceleration_tick
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;a4988_enable
</UL>

<P><STRONG><a name="[ac]"></a>i2c_ack</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, i2c.o(i.i2c_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = i2c_ack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
</UL>

<P><STRONG><a name="[ad]"></a>i2c_delay</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, i2c.o(i.i2c_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_nack
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_ack
</UL>

<P><STRONG><a name="[af]"></a>i2c_nack</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, i2c.o(i.i2c_nack))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
</UL>

<P><STRONG><a name="[b4]"></a>i2c_read_byte</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, i2c.o(i.i2c_read_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_nack
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_ack
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_bytes
</UL>

<P><STRONG><a name="[b1]"></a>i2c_send_byte</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, i2c.o(i.i2c_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = i2c_send_byte &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_bytes
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_bytes
</UL>

<P><STRONG><a name="[b0]"></a>i2c_start</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, i2c.o(i.i2c_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = i2c_start &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_bytes
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_bytes
</UL>

<P><STRONG><a name="[b3]"></a>i2c_stop</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, i2c.o(i.i2c_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = i2c_stop &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_bytes
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_bytes
</UL>

<P><STRONG><a name="[b2]"></a>i2c_wait_ack</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, i2c.o(i.i2c_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = i2c_wait_ack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_bytes
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_bytes
</UL>

<P><STRONG><a name="[9f]"></a>configure_address</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, vl53l0x.o(i.configure_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = configure_address &rArr; i2c_write_addr8_data8 &rArr; i2c_send_byte &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_address
</UL>

<P><STRONG><a name="[a1]"></a>configure_gpio</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, vl53l0x.o(i.configure_gpio))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = configure_gpio &rArr; gpio_init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_set_output
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_addresses
</UL>

<P><STRONG><a name="[a4]"></a>configure_interrupt</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, vl53l0x.o(i.configure_interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = configure_interrupt &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;static_init
</UL>

<P><STRONG><a name="[a6]"></a>data_init</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, vl53l0x.o(i.data_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = data_init &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_config
</UL>

<P><STRONG><a name="[a7]"></a>device_is_booted</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, vl53l0x.o(i.device_is_booted))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = device_is_booted &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_address
</UL>

<P><STRONG><a name="[a8]"></a>get_spad_info_from_nvm</STRONG> (Thumb, 286 bytes, Stack size 32 bytes, vl53l0x.o(i.get_spad_info_from_nvm))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = get_spad_info_from_nvm &rArr; read_strobe &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_strobe
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data32
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_bytes
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_spads_from_nvm
</UL>

<P><STRONG><a name="[b7]"></a>init_address</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, vl53l0x.o(i.init_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = init_address &rArr; device_is_booted &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_hardware_standby
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_is_booted
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;configure_address
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_set_slave_address
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_addresses
</UL>

<P><STRONG><a name="[ba]"></a>init_addresses</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, vl53l0x.o(i.init_addresses))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = init_addresses &rArr; init_address &rArr; device_is_booted &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_address
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;configure_gpio
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_init
</UL>

<P><STRONG><a name="[bb]"></a>init_config</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, vl53l0x.o(i.init_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = init_config &rArr; static_init &rArr; set_spads_from_nvm &rArr; get_spad_info_from_nvm &rArr; read_strobe &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;static_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_ref_calibration
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_set_slave_address
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vl53l0x_init
</UL>

<P><STRONG><a name="[be]"></a>load_default_tuning_settings</STRONG> (Thumb, 806 bytes, Stack size 8 bytes, vl53l0x.o(i.load_default_tuning_settings))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = load_default_tuning_settings &rArr; i2c_write_addr8_data8 &rArr; i2c_send_byte &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;static_init
</UL>

<P><STRONG><a name="[bd]"></a>perform_ref_calibration</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, vl53l0x.o(i.perform_ref_calibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = perform_ref_calibration &rArr; perform_single_ref_calibration &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_steps_enabled
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_single_ref_calibration
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_config
</UL>

<P><STRONG><a name="[d3]"></a>perform_single_ref_calibration</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, vl53l0x.o(i.perform_single_ref_calibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = perform_single_ref_calibration &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_ref_calibration
</UL>

<P><STRONG><a name="[a9]"></a>read_strobe</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, vl53l0x.o(i.read_strobe))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = read_strobe &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_addr8_data8
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_spad_info_from_nvm
</UL>

<P><STRONG><a name="[b8]"></a>set_hardware_standby</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, vl53l0x.o(i.set_hardware_standby))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = set_hardware_standby &rArr; gpio_set_output
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_set_output
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_address
</UL>

<P><STRONG><a name="[d4]"></a>set_sequence_steps_enabled</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, vl53l0x.o(i.set_sequence_steps_enabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = set_sequence_steps_enabled &rArr; i2c_write_addr8_data8 &rArr; i2c_send_byte &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;static_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perform_ref_calibration
</UL>

<P><STRONG><a name="[d5]"></a>set_spads_from_nvm</STRONG> (Thumb, 258 bytes, Stack size 48 bytes, vl53l0x.o(i.set_spads_from_nvm))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = set_spads_from_nvm &rArr; get_spad_info_from_nvm &rArr; read_strobe &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_spad_info_from_nvm
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_data8
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_addr8_bytes
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;static_init
</UL>

<P><STRONG><a name="[bc]"></a>static_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, vl53l0x.o(i.static_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = static_init &rArr; set_spads_from_nvm &rArr; get_spad_info_from_nvm &rArr; read_strobe &rArr; i2c_read_addr8_data8 &rArr; i2c_read_byte &rArr; i2c_nack &rArr; i2c_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_spads_from_nvm
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sequence_steps_enabled
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_default_tuning_settings
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;configure_interrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_config
</UL>

<P><STRONG><a name="[eb]"></a>_fadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
</UL>

<P><STRONG><a name="[df]"></a>_fsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
