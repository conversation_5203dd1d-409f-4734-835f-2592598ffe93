#include "i2c.h"

static uint8_t current_slave_addr = 0;


// 底层软件模拟函数


// 微秒级延时，用于控制I2C速度
static void i2c_delay(void)
{
    // 此延时决定了I2C时钟频率。对于72MHz的STM32，这个循环
    // 大约产生几微秒的延时，对应于约100-200kHz的SCL频率。
    // 你可以调整循环次数来改变速度。
    volatile uint8_t i = 10;
    while (i--);
}

// 定义引脚操作宏
#define SCL_H() GPIO_SetBits(I2C_PORT, I2C_SCL_PIN)
#define SCL_L() GPIO_ResetBits(I2C_PORT, I2C_SCL_PIN)
#define SDA_H() GPIO_SetBits(I2C_PORT, I2C_SDA_PIN)
#define SDA_L() GPIO_ResetBits(I2C_PORT, I2C_SDA_PIN)
#define SDA_READ() GPIO_ReadInputDataBit(I2C_PORT, I2C_SDA_PIN)

/**
 * @brief 产生I2C起始信号
 */
static void i2c_start(void)
{
    SDA_H();
    SCL_H();
    i2c_delay();
    SDA_L();
    i2c_delay();
    SCL_L();
    i2c_delay();
}

/**
 * @brief 产生I2C停止信号
 */
static void i2c_stop(void)
{
    SDA_L();
    SCL_L();
    i2c_delay();
    SCL_H();
    i2c_delay();
    SDA_H();
    i2c_delay();
}

/**
 * @brief 等待从设备的应答信号
 * @return bool true: 收到应答, false: 未收到应答
 */
static bool i2c_wait_ack(void)
{
    bool ack;
    SCL_L();
    SDA_H(); // 主机释放SDA总线
    i2c_delay();
    SCL_H();
    i2c_delay();
    ack = (SDA_READ() == 0); // 从机拉低SDA表示应答
    SCL_L();
    i2c_delay();
    return ack;
}

/**
 * @brief 主机发送应答信号
 */
static void i2c_ack(void)
{
    SCL_L();
    SDA_L();
    i2c_delay();
    SCL_H();
    i2c_delay();
    SCL_L();
}

/**
 * @brief 主机发送非应答信号
 */
static void i2c_nack(void)
{
    SCL_L();
    SDA_H();
    i2c_delay();
    SCL_H();
    i2c_delay();
    SCL_L();
}

/**
 * @brief 发送一个字节
 * @param byte 要发送的字节
 */
static void i2c_send_byte(uint8_t byte)
{
    uint8_t i;
    SCL_L();
    for (i = 0; i < 8; i++)
    {
        if (byte & 0x80)
        {
            SDA_H();
        }
        else
        {
            SDA_L();
        }
        byte <<= 1;
        i2c_delay();
        SCL_H();
        i2c_delay();
        SCL_L();
    }
}

/**
 * @brief 读取一个字节
 * @param ack true: 发送ACK, false: 发送NACK
 * @return uint8_t 读取到的字节
 */
static uint8_t i2c_read_byte(bool ack)
{
    uint8_t i, byte = 0;
    SDA_H(); // 释放SDA，准备接收
    for (i = 0; i < 8; i++)
    {
        SCL_L();
        i2c_delay();
        SCL_H();
        byte <<= 1;
        if (SDA_READ())
        {
            byte |= 0x01;
        }
        i2c_delay();
    }
    SCL_L();

    if (ack)
    {
        i2c_ack();
    }
    else
    {
        i2c_nack();
    }
    return byte;
}



bool i2c_init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    /* 使能GPIO时钟 */
    RCC_APB2PeriphClockCmd(I2C_GPIO_CLK, ENABLE);

    /* 配置I2C引脚为开漏输出 */
    GPIO_InitStructure.GPIO_Pin = I2C_SCL_PIN | I2C_SDA_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_OD;
    GPIO_Init(I2C_PORT, &GPIO_InitStructure);

    /* 初始化时将SCL和SDA都拉高，表示总线空闲 */
    SCL_H();
    SDA_H();
    
    return true;
}

void i2c_set_slave_address(uint8_t addr)
{
    current_slave_addr = addr;
}

bool i2c_read_addr8_data8(uint8_t reg_addr, uint8_t *data)
{
    i2c_start();
    i2c_send_byte(current_slave_addr << 1 | 0); // 写地址
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    i2c_send_byte(reg_addr);
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    i2c_start(); // 重复起始信号
    i2c_send_byte(current_slave_addr << 1 | 1); // 读地址
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    *data = i2c_read_byte(false); // 读取一个字节并发送NACK
    i2c_stop();
    return true;
}

bool i2c_read_addr8_data16(uint8_t reg_addr, uint16_t *data)
{
    uint8_t buf[2];
    if (!i2c_read_addr8_bytes(reg_addr, buf, 2))
    {
        return false;
    }
    *data = ((uint16_t)buf[0] << 8) | buf[1];
    return true;
}

bool i2c_read_addr8_data32(uint8_t reg_addr, uint32_t *data)
{
    uint8_t buf[4];
    if (!i2c_read_addr8_bytes(reg_addr, buf, 4))
    {
        return false;
    }
    *data = ((uint32_t)buf[0] << 24) | ((uint32_t)buf[1] << 16) | ((uint32_t)buf[2] << 8) | buf[3];
    return true;
}

bool i2c_read_addr8_bytes(uint8_t reg_addr, uint8_t *data, uint8_t len)
{
    if (len == 0) return true;

    i2c_start();
    i2c_send_byte(current_slave_addr << 1 | 0); // 写地址
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    i2c_send_byte(reg_addr);
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    i2c_start(); // 重复起始信号
    i2c_send_byte(current_slave_addr << 1 | 1); // 读地址
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    while (len > 1)
    {
        *data++ = i2c_read_byte(true); // 读取字节并发送ACK
        len--;
    }
    *data = i2c_read_byte(false); // 读取最后一个字节并发送NACK
    
    i2c_stop();
    return true;
}

bool i2c_write_addr8_data8(uint8_t reg_addr, uint8_t data)
{
    i2c_start();
    i2c_send_byte(current_slave_addr << 1 | 0); // 写地址
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    i2c_send_byte(reg_addr);
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    i2c_send_byte(data);
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    i2c_stop();
    return true;
}

bool i2c_write_addr8_bytes(uint8_t reg_addr, const uint8_t *data, uint8_t len)
{
    i2c_start();
    i2c_send_byte(current_slave_addr << 1 | 0); // 写地址
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    i2c_send_byte(reg_addr);
    if (!i2c_wait_ack())
    {
        i2c_stop();
        return false;
    }

    for (uint8_t i = 0; i < len; i++)
    {
        i2c_send_byte(data[i]);
        if (!i2c_wait_ack())
        {
            i2c_stop();
            return false;
        }
    }

    i2c_stop();
    return true;
} 
