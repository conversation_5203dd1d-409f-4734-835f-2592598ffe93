#ifndef __DELAY_H
#define __DELAY_H

#include "stm32f10x.h"
#include "a4988.h"  // 包含a4988.h以访问stepper1_state

// 延时状态结构体
typedef struct {
    volatile uint32_t counter;
} DelayState_t;
extern volatile DelayState_t delay_state;

// 初始化函数
void Delay_Init(void);

// 延时函数
void Delay_us(uint32_t us);
void Delay_ms(uint32_t ms);
void Delay_s(uint32_t s);

// 时钟获取函数
uint32_t Delay_GetTick(void);

#endif /* __DELAY_H */
