#include "usart.h"
#include "stm32f10x_gpio.h"
#include "stm32f10x_usart.h"
#include "stm32f10x_rcc.h"
#include "misc.h"

/* Private defines */
#define USART_PORT                GPIOA
#define USART_TX_PIN              GPIO_Pin_9
#define USART_RX_PIN              GPIO_Pin_10
#define USART_GPIO_CLK            RCC_APB2Periph_GPIOA
#define USART_CLK                 RCC_APB2Periph_USART1
#define USART_IRQn                USART1_IRQn

/* Private variables */
static void (*receiveCallback)(uint8_t data) = 0;
static volatile uint8_t isInitialized = 0;
static volatile uint8_t interruptEnabled = 0;

/**
 * @brief Initialize USART peripheral
 * @param usartConfig Pointer to USART configuration structure
 * @return UsartStatus_t Status of initialization
 */
UsartStatus_t usartInit(const UsartConfig_t* usartConfig) {
    GPIO_InitTypeDef gpioInitStruct;
    USART_InitTypeDef usartInitStruct;
    NVIC_InitTypeDef nvicInitStruct;
    
    if (usartConfig == 0) {
        return USART_ERROR;
    }
    
    /* Enable GPIO and USART clocks */
    RCC_APB2PeriphClockCmd(USART_GPIO_CLK, ENABLE);
    RCC_APB2PeriphClockCmd(USART_CLK, ENABLE);
    
    /* Configure GPIO pins */
    gpioInitStruct.GPIO_Pin = USART_TX_PIN;
    gpioInitStruct.GPIO_Mode = GPIO_Mode_AF_PP;
    gpioInitStruct.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(USART_PORT, &gpioInitStruct);
    
    gpioInitStruct.GPIO_Pin = USART_RX_PIN;
    gpioInitStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(USART_PORT, &gpioInitStruct);
    
    /* Configure USART */
    usartInitStruct.USART_BaudRate = usartConfig->baudRate;
    usartInitStruct.USART_WordLength = (usartConfig->dataBits == 9) ? USART_WordLength_9b : USART_WordLength_8b;
    usartInitStruct.USART_StopBits = (usartConfig->stopBits == 2) ? USART_StopBits_2 : USART_StopBits_1;
    
    switch (usartConfig->parity) {
        case 1:
            usartInitStruct.USART_Parity = USART_Parity_Even;
            break;
        case 2:
            usartInitStruct.USART_Parity = USART_Parity_Odd;
            break;
        default:
            usartInitStruct.USART_Parity = USART_Parity_No;
            break;
    }
    
    usartInitStruct.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    usartInitStruct.USART_HardwareFlowControl = (usartConfig->flowControl == 1) ? 
                                                USART_HardwareFlowControl_RTS_CTS : 
                                                USART_HardwareFlowControl_None;
    
    USART_Init(USART1, &usartInitStruct);
    
    /* Configure NVIC for USART interrupts */
    nvicInitStruct.NVIC_IRQChannel = USART_IRQn;
    nvicInitStruct.NVIC_IRQChannelPreemptionPriority = 0;
    nvicInitStruct.NVIC_IRQChannelSubPriority = 0;
    nvicInitStruct.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&nvicInitStruct);
    
    /* Enable USART */
    USART_Cmd(USART1, ENABLE);
    
    isInitialized = 1;
    interruptEnabled = 0;
    return USART_OK;
}

/**
 * @brief Send a single byte via USART
 * @param data Byte to send
 * @return UsartStatus_t Status of transmission
 */
UsartStatus_t usartSendByte(uint8_t data) {
    if (!isInitialized) {
        return USART_ERROR;
    }
    
    /* Wait until the transmit data register is empty */
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);
    
    /* Send the byte */
    USART_SendData(USART1, data);
    
    /* Wait until transmission is complete */
    while (USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET);
    
    return USART_OK;
}

/**
 * @brief Send multiple bytes via USART
 * @param data Pointer to data buffer
 * @param length Number of bytes to send
 * @return UsartStatus_t Status of transmission
 */
UsartStatus_t usartSendData(const uint8_t* data, uint16_t length) {
    if (!isInitialized || data == 0) {
        return USART_ERROR;
    }
    
    for (uint16_t i = 0; i < length; i++) {
        UsartStatus_t status = usartSendByte(data[i]);
        if (status != USART_OK) {
            return status;
        }
    }
    
    return USART_OK;
}

/**
 * @brief Send a string via USART
 * @param string Null-terminated string to send
 * @return UsartStatus_t Status of transmission
 */
UsartStatus_t usartSendString(const char* string) {
    if (!isInitialized || string == 0) {
        return USART_ERROR;
    }
    
    uint16_t length = 0;
    while (string[length] != '\0') {
        length++;
    }
    
    return usartSendData((const uint8_t*)string, length);
}

/**
 * @brief Receive a single byte via USART
 * @param data Pointer to store received byte
 * @param timeout Timeout in milliseconds (0 for no timeout)
 * @return UsartStatus_t Status of reception
 */
UsartStatus_t usartReceiveByte(uint8_t* data, uint32_t timeout) {
    if (!isInitialized || data == 0) {
        return USART_ERROR;
    }
    
    /* Wait for data to be received */
    while (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == RESET) {
        if (timeout > 0) {
            /* Simple timeout implementation */
            volatile uint32_t delay = timeout * 1000;
            while (delay--);
            if (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == RESET) {
                return USART_TIMEOUT;
            }
        }
    }
    
    /* Read the received data */
    *data = USART_ReceiveData(USART1);
    
    return USART_OK;
}

/**
 * @brief Receive multiple bytes via USART
 * @param data Pointer to data buffer
 * @param length Number of bytes to receive
 * @param timeout Timeout in milliseconds (0 for no timeout)
 * @return UsartStatus_t Status of reception
 */
UsartStatus_t usartReceiveData(uint8_t* data, uint16_t length, uint32_t timeout) {
    if (!isInitialized || data == 0) {
        return USART_ERROR;
    }
    
    for (uint16_t i = 0; i < length; i++) {
        UsartStatus_t status = usartReceiveByte(&data[i], timeout);
        if (status != USART_OK) {
            return status;
        }
    }
    
    return USART_OK;
}

/**
 * @brief Check if USART is ready to send data
 * @return uint8_t True if ready to send
 */
uint8_t usartIsReadyToSend(void) {
    if (!isInitialized) {
        return 0;
    }
    
    return (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == SET) ? 1 : 0;
}

/**
 * @brief Check if USART has received data
 * @return uint8_t True if data is available
 */
uint8_t usartHasData(void) {
    if (!isInitialized) {
        return 0;
    }
    
    return (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == SET) ? 1 : 0;
}

/**
 * @brief Enable USART receive interrupt
 * @return UsartStatus_t Status of operation
 */
UsartStatus_t usartEnableReceiveInterrupt(void) {
    if (!isInitialized) {
        return USART_ERROR;
    }
    
    USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
    interruptEnabled = 1;
    return USART_OK;
}

/**
 * @brief Disable USART receive interrupt
 * @return UsartStatus_t Status of operation
 */
UsartStatus_t usartDisableReceiveInterrupt(void) {
    if (!isInitialized) {
        return USART_ERROR;
    }
    
    USART_ITConfig(USART1, USART_IT_RXNE, DISABLE);
    interruptEnabled = 0;
    return USART_OK;
}

/**
 * @brief Set receive callback function
 * @param callback Function pointer to callback
 */
void usartSetReceiveCallback(void (*callback)(uint8_t data)) {
    receiveCallback = callback;
}

/**
 * @brief Process USART interrupt (to be called from main loop or timer)
 * @return uint8_t True if interrupt was processed
 */
uint8_t usartProcessInterrupt(void) {
    if (!isInitialized || !interruptEnabled) {
        return 0;
    }
    
    if (USART_GetITStatus(USART1, USART_IT_RXNE) != RESET) {
        /* Clear the interrupt flag */
        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
        
        /* Read the received data */
        uint8_t receivedData = USART_ReceiveData(USART1);
        
        /* Call the callback function if set */
        if (receiveCallback != 0) {
            receiveCallback(receivedData);
        }
        
        return 1;
    }
    
    return 0;
}

/**
 * @brief Get a received byte if available (non-blocking)
 * @param data Pointer to store received byte
 * @return uint8_t True if data was available
 */
uint8_t usartGetReceivedByte(uint8_t* data) {
    if (!isInitialized || data == 0) {
        return 0;
    }
    
    if (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == SET) {
        *data = USART_ReceiveData(USART1);
        return 1;
    }
    
    return 0;
	}
