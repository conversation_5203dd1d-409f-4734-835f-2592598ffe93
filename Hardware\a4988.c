#include "a4988.h"
#include "stm32f10x.h"                  // Device header
#include "Delay.h"

/**
 * @brief 电机1状态 (全局变量)
 * @note  用于软件计数法控制电机1脉冲、方向、步数等
 */
Stepper1_State_t stepper1_state = {0};

/**
 * @brief 电机2状态 (全局变量)
 * @note  用于定时器PWM法控制电机2，支持平滑加减速
 */
Stepper2_State_t stepper2_state = {0};

/**
 * @brief 电机2脉冲计数变量（用于角度统计）
 */
volatile int32_t stepper2_pulse_count = 0; // 电机2脉冲计数

/**
 * @brief 电机2脉冲控制结构体 - 带平缓调速
 */
typedef struct {
    volatile uint32_t target_pulses;    // 目标脉冲数
    volatile uint32_t current_pulses;   // 当前已发送脉冲数
    volatile uint8_t is_running;        // 运行状态 (1=运行, 0=停止)

    // 平缓调速参数
    volatile uint16_t current_freq;     // 当前频率 (Hz)
    volatile uint16_t target_freq;      // 目标频率 (Hz)
    volatile uint16_t min_freq;         // 最小频率 (Hz)
    volatile uint16_t max_freq;         // 最大频率 (Hz)
    volatile uint16_t accel_step;       // 加速步长 (Hz/次)
    volatile uint32_t total_time_ms;    // 总运行时间 (毫秒)
    volatile uint32_t start_time;       // 开始时间戳

    // 调速阶段
    volatile uint8_t phase;             // 0=加速, 1=匀速, 2=减速
    volatile uint32_t accel_pulses;     // 加速阶段脉冲数
    volatile uint32_t decel_start_pulses; // 减速开始脉冲数
} Motor2_SmoothCtrl_t;

Motor2_SmoothCtrl_t motor2_smooth = {0};

/**
 * @brief 电机同步控制结构体
 */
typedef struct {
    volatile uint8_t sync_enabled;      // 同步模式使能
    volatile uint32_t motor1_start_steps; // 电机1开始时的步数
    volatile uint32_t motor1_target_steps; // 电机1目标步数
    volatile uint32_t motor2_start_pulses; // 电机2开始时的脉冲数
    volatile uint32_t motor2_target_pulses; // 电机2目标脉冲数
    volatile uint32_t sync_start_time;   // 同步开始时间
} MotorSync_t;

MotorSync_t motor_sync = {0};

// ================= 定时器定义 =================
#define STEPPER_TIMER TIM2 ///< 用于电机2的定时器

// 内部函数声明
static void _a4988_update_pwm(uint16_t rpm); // 更新PWM频率
static void _a4988_test_pwm(void);  // 添加PWM测试函数

/**
 * @brief TIM2中断服务函数（仅用于电机2步数统计）
 * @note  电机2采用定时器PWM方式，步数在中断中统计
 */
void TIM2_IRQHandler(void)
{
    // 处理通道2中断（电机2脉冲控制 - 兼容新旧系统）
    if (TIM_GetITStatus(TIM2, TIM_IT_CC2) == SET) {
        TIM_ClearITPendingBit(TIM2, TIM_IT_CC2);

        // 角度计数（两套系统都需要）
        if (stepper2_state.direction) {
            stepper2_pulse_count++;
        } else {
            stepper2_pulse_count--;
        }

        // 新系统：平缓调速脉冲控制
        if (motor2_smooth.is_running) {
            motor2_smooth.current_pulses++;

            // 检查是否完成
            if (motor2_smooth.current_pulses >= motor2_smooth.target_pulses) {
                TIM_Cmd(STEPPER_TIMER, DISABLE);  // 停止定时器
                motor2_smooth.is_running = 0;     // 标记完成
                a4988_enable(2, false);           // 禁用电机
            }
        }
        // 注意：旧系统的PWM控制由_a4988_update_pwm()和stepper2_acceleration_tick()处理
    }
    // 清除更新中断标志
    if (TIM_GetITStatus(TIM2, TIM_IT_Update) == SET) {
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
    }
}

/**
 * @brief 步进电机初始化，配置引脚和定时器
 */
void a4988_init(void)
{
    GPIO_InitTypeDef gpio;
    TIM_TimeBaseInitTypeDef tim;
    TIM_OCInitTypeDef oc;

    // 使能GPIO和定时器时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_AFIO, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);

    // 配置所有引脚为推挽输出
    gpio.GPIO_Mode = GPIO_Mode_Out_PP;
    gpio.GPIO_Speed = GPIO_Speed_50MHz;
    gpio.GPIO_Pin = STEPPER1_STEP_PIN | STEPPER1_DIR_PIN | STEPPER1_ENABLE_PIN |
                    STEPPER2_DIR_PIN | STEPPER2_ENABLE_PIN;
    GPIO_Init(GPIOA, &gpio);
    
    // 电机2的STEP引脚配置为复用推挽（用于PWM输出）
    gpio.GPIO_Mode = GPIO_Mode_AF_PP;
    gpio.GPIO_Pin = STEPPER2_STEP_PIN;
    GPIO_Init(STEPPER2_PORT, &gpio);

    // 初始化所有引脚为默认状态
    GPIO_ResetBits(STEPPER1_PORT, STEPPER1_STEP_PIN);
    GPIO_ResetBits(STEPPER1_PORT, STEPPER1_DIR_PIN);
    GPIO_SetBits(STEPPER1_PORT, STEPPER1_ENABLE_PIN);   // 禁用电机1
    GPIO_ResetBits(STEPPER2_PORT, STEPPER2_DIR_PIN);
    GPIO_SetBits(STEPPER2_PORT, STEPPER2_ENABLE_PIN);   // 禁用电机2

    // 定时器基本配置 (用于电机2)
    tim.TIM_Period = 999;           // 初始ARR值 (1kHz频率)
    tim.TIM_Prescaler = 71;         // 72MHz/72 = 1MHz
    tim.TIM_ClockDivision = TIM_CKD_DIV1;
    tim.TIM_CounterMode = TIM_CounterMode_Up;
    tim.TIM_RepetitionCounter = 0;
    TIM_TimeBaseInit(STEPPER_TIMER, &tim);

    // PWM输出配置 (电机2)
    oc.TIM_OCMode = TIM_OCMode_PWM1;
    oc.TIM_OutputState = TIM_OutputState_Enable;
    oc.TIM_Pulse = 500;  // 初始比较值500 (50%占空比)
    oc.TIM_OCPolarity = TIM_OCPolarity_High;
    // 通道2配置
    TIM_OC2Init(STEPPER_TIMER, &oc);
    TIM_OC2PreloadConfig(STEPPER_TIMER, TIM_OCPreload_Enable);

    // 使能定时器
    TIM_ARRPreloadConfig(STEPPER_TIMER, ENABLE);
    TIM_ITConfig(TIM2, TIM_IT_CC2 | TIM_IT_Update, ENABLE);
    TIM_Cmd(STEPPER_TIMER, ENABLE);
    
    // 立即使能PWM输出
    TIM_CtrlPWMOutputs(STEPPER_TIMER, ENABLE);

    // 配置中断优先级
    NVIC_InitTypeDef nvic;
    nvic.NVIC_IRQChannel = TIM2_IRQn;
    nvic.NVIC_IRQChannelPreemptionPriority = 0;
    nvic.NVIC_IRQChannelSubPriority = 1;
    nvic.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&nvic);

    // 初始化电机1状态
    stepper1_state.interval = 0;
    stepper1_state.counter = 0;
    stepper1_state.flag = 0;
    stepper1_state.steps = 0;
    stepper1_state.direction = false;
    stepper1_state.enabled = false;

    // 初始化电机2状态
    stepper2_state.current_rpm = 0;
    stepper2_state.target_rpm = 0;
    stepper2_state.accel_step = 40;      // 适中的加速度步进
    stepper2_state.update_interval = 5; // 减少更新间隔，约100ms一次更新
    stepper2_state.update_counter = 0;
    stepper2_state.steps = 0;
    stepper2_state.direction = false;
    stepper2_state.enabled = false;
}

// ================= 通用控制函数 =================
/**
 * @brief 设置指定电机方向
 * @param stepper_id 电机编号（1或2）
 * @param direction  方向（true为正转，false为反转）
 */
void a4988_set_direction(uint8_t stepper_id, bool direction)
{
    if (stepper_id == 1) {
        stepper1_state.direction = direction;
        GPIO_WriteBit(STEPPER1_PORT, STEPPER1_DIR_PIN, direction ? Bit_SET : Bit_RESET);
    } else {
        stepper2_state.direction = direction;
        GPIO_WriteBit(STEPPER2_PORT, STEPPER2_DIR_PIN, direction ? Bit_SET : Bit_RESET);
    }
}

/**
 * @brief 使能或禁用指定电机
 * @param stepper_id 电机编号（1或2）
 * @param enable     true为使能，false为禁用
 */
void a4988_enable(uint8_t stepper_id, bool enable)
{
    if (stepper_id == 1) {
        // 低电平使能
        GPIO_WriteBit(STEPPER1_PORT, STEPPER1_ENABLE_PIN, enable ? Bit_RESET : Bit_SET);
        stepper1_state.enabled = enable;
    } else {
        // 低电平使能
        GPIO_WriteBit(STEPPER2_PORT, STEPPER2_ENABLE_PIN, enable ? Bit_RESET : Bit_SET);
        stepper2_state.enabled = enable;
        if (enable) {
            // 修复：使能时从0开始加速，不跳过加速过程
            stepper2_state.current_rpm = 0;  // 重置当前转速为0
            stepper2_state.update_counter = 0;  // 重置更新计数器
            // 不要调用_a4988_update_pwm(0)，让加速函数来处理PWM启动
        } else {
            // 如果禁用电机，则立即停止PWM并重置速度
            a4988_set_speed2(0);
            stepper2_state.current_rpm = 0;
            _a4988_update_pwm(0);
        }
    }
}

/**
 * @brief 获取指定电机累计转角（单位：度）
 * @param stepper_id 电机编号（1或2）
 * @return 当前累计角度（可正可负）
 */
int32_t a4988_get_angle(uint8_t stepper_id)
{
    if (stepper_id == 1) {
        return (stepper1_state.steps * 360) / STEPS_PER_REV;
    } else {
        // 用脉冲计数变量计算角度
        return (stepper2_pulse_count * 360) / STEPS_PER_REV;
    }
}

/**
 * @brief 重置指定电机累计角度/步数
 * @param stepper_id 电机编号（1或2）
 */
void a4988_reset_angle(uint8_t stepper_id)
{
    if (stepper_id == 1) {
        stepper1_state.steps = 0;
    } else {
        stepper2_state.steps = 0;
        stepper2_pulse_count = 0; // 角度归零
    }
}

// ================= 电机1特定函数 =================
/**
 * @brief 设置电机1转速（软件计数法）
 * @param rpm 目标转速（转/分钟）
 * @note 通过设置interval实现速度调节，主循环需调用stepper1_software_tick
 */
void a4988_set_speed1(uint16_t rpm)
{
    if (rpm == 0) {
        stepper1_state.interval = 0; // 停止
        return;
    }
    // 计算脉冲间隔 (10us单位)
    float step_freq = (rpm * STEPS_PER_REV) / 60.0f;  // Hz
    stepper1_state.interval = (uint32_t)(100000.0f / step_freq); // 100kHz时钟
    // 重置计数器
    stepper1_state.counter = 0;
}

/**
 * @brief 电机1主循环脉冲处理（软件计数法）
 * @note 需在主循环中高频率调用
 */
void stepper1_software_tick(void)
{
    if (!stepper1_state.enabled || stepper1_state.interval == 0) return;
    // ====== 步数运动自动停止逻辑 ======
    if (stepper1_busy) {
        int32_t moved = stepper1_state.steps - stepper1_start_step;
        if ((stepper1_target_steps > 0 && moved >= stepper1_target_steps) ||
            (stepper1_target_steps < 0 && moved <= stepper1_target_steps)) {
            a4988_set_speed1(0);
            a4988_enable(1, false);
            stepper1_busy = 0;
        }
    }
    stepper1_state.counter++;
    if (stepper1_state.counter >= stepper1_state.interval) {
        stepper1_state.counter = 0;
        // 产生脉冲
        GPIO_SetBits(STEPPER1_PORT, STEPPER1_STEP_PIN);
        for (volatile int i = 0; i < 250; i++); // 约5us (增加脉冲宽度)
        GPIO_ResetBits(STEPPER1_PORT, STEPPER1_STEP_PIN);
        // 步数统计
        stepper1_state.steps += stepper1_state.direction ? 1 : -1;
    }
}

// ================= 电机2特定函数 =================
/**
 * @brief 设置电机2转速（PWM法，带加减速）
 * @param rpm 目标转速（转/分钟）
 * @note 仅设置目标速度，实际速度由 `stepper2_acceleration_tick` 控制
 */
void a4988_set_speed2(uint16_t rpm)
{
    stepper2_state.target_rpm = rpm;
}

// ====== 新增：步数运动相关变量 ======
volatile int32_t stepper2_target_pulses = 0; // 目标脉冲数
volatile int32_t stepper2_start_pulse = 0;   // 运动起点
volatile uint8_t stepper2_busy = 0;          // 是否正在运动

/**
 * @brief 启动电机2步数运动（非阻塞，带自定义速度）
 * @param steps 目标步数（正为正转，负为反转）
 * @param speed 运动速度（RPM）
 */
void a4988_start_move_steps2_with_speed(int32_t steps, uint16_t speed)
{
    if (steps == 0) return;
    stepper2_start_pulse = stepper2_pulse_count;
    stepper2_target_pulses = steps;
    stepper2_busy = 1;
    if (steps > 0) {
       a4988_set_direction(2, 1);
    } else {
       a4988_set_direction(2, 0);
    }
    a4988_enable(2, true);
    a4988_set_speed2(speed); // 使用自定义速度
}

/**
 * @brief 启动电机2步数运动（非阻塞）
 * @param steps 目标步数（正为正转，负为反转）
 */
void a4988_start_move_steps2(int32_t steps)
{
    a4988_start_move_steps2_with_speed(steps, 100); // 默认100 RPM
}

/**
 * @brief 电机2主循环加减速处理
 * @note 需在主循环中高频率调用，以实现平滑速度变化和步数运动自动停止
 * @note 你可以在此函数内根据剩余步数动态调整target_rpm，实现平滑减速到0点
 */
void stepper2_acceleration_tick(void)
{
    if (!stepper2_state.enabled) {
        return;
    }

    // ====== 步数运动自动停止逻辑 ======
    if (stepper2_busy) {
        int32_t moved = stepper2_pulse_count - stepper2_start_pulse;
        int32_t remain = stepper2_target_pulses - moved; // 剩余步数
        int32_t remain_abs = remain > 0 ? remain : -remain;

        if ((stepper2_target_pulses > 0 && moved >= stepper2_target_pulses) ||
            (stepper2_target_pulses < 0 && moved <= stepper2_target_pulses)) {
            a4988_set_speed2(0);
            a4988_enable(2, false);
            stepper2_busy = 0;
        }
    }

    stepper2_state.update_counter++;
    if (stepper2_state.update_counter < stepper2_state.update_interval) {
        return; // 未到更新时间
    }
    stepper2_state.update_counter = 0; // 重置计数器

    // 修复：如果目标转速大于0且当前转速为0，立即启动PWM
    if (stepper2_state.target_rpm > 0 && stepper2_state.current_rpm == 0) {
        stepper2_state.current_rpm = stepper2_state.accel_step; // 立即启动
        _a4988_update_pwm(stepper2_state.current_rpm);
    }
    else if (stepper2_state.current_rpm < stepper2_state.target_rpm) {
        stepper2_state.current_rpm += stepper2_state.accel_step;
        if (stepper2_state.current_rpm > stepper2_state.target_rpm) {
            stepper2_state.current_rpm = stepper2_state.target_rpm;
        }
        _a4988_update_pwm(stepper2_state.current_rpm);
    }
    else if (stepper2_state.current_rpm > stepper2_state.target_rpm) {
        if (stepper2_state.current_rpm > stepper2_state.accel_step) {
            stepper2_state.current_rpm -= stepper2_state.accel_step;
        } else {
            stepper2_state.current_rpm = 0;
        }
        if (stepper2_state.current_rpm < stepper2_state.target_rpm) {
            stepper2_state.current_rpm = stepper2_state.target_rpm;
        }
        _a4988_update_pwm(stepper2_state.current_rpm);
    }
}

/**
 * @brief 根据RPM更新电机2的PWM输出（内部函数）
 * @param rpm 目标转速（转/分钟）
 * @note 直接修改定时器参数来改变PWM频率
 */
static void _a4988_update_pwm(uint16_t rpm)
{
    if (rpm == 0) {
        TIM_SetCompare2(STEPPER_TIMER, 0);
        return;
    }
    // 计算步进频率
    float step_freq = (rpm * STEPS_PER_REV) / 60.0f;
    // 计算周期时间 (us) - 使用1MHz时钟
    float period_us = 1000000.0f / step_freq;
    // 转换为定时器计数值 (1MHz时钟，1us = 1 tick)
    uint32_t period_ticks = (uint32_t)(period_us);
    // 限制范围，确保脉冲宽度足够
    if (period_ticks < 100) period_ticks = 100;   // 最小10kHz，确保脉冲宽度
    if (period_ticks > 65530) period_ticks = 65530; // 配合16位定时器
    // 计算ARR和CCR（固定20%占空比，确保脉冲宽度足够）
    uint32_t arr = period_ticks - 1;
    uint32_t ccr = arr / 5;  // 20%占空比，确保脉冲宽度
    
    // 确保CCR不为0，避免无脉冲输出
    if (ccr == 0) ccr = 1;
    
    // 更新定时器
    TIM_SetAutoreload(STEPPER_TIMER, arr);
    TIM_SetCompare2(STEPPER_TIMER, ccr);
    // 立即让新参数生效
    TIM_GenerateEvent(STEPPER_TIMER, TIM_EventSource_Update);
}

/**
 * @brief PWM测试函数（内部函数）
 * @note 直接设置固定的PWM参数，用于验证PWM输出是否正常
 */
static void _a4988_test_pwm(void)
{
    // 设置固定的PWM参数：1kHz频率，50%占空比
    uint32_t arr = 999;  // 1MHz/1000 = 1kHz
    uint32_t ccr = 500;  // 50%占空比
    
    // 更新定时器
    TIM_SetAutoreload(STEPPER_TIMER, arr);
    TIM_SetCompare2(STEPPER_TIMER, ccr);
    // 立即让新参数生效
    TIM_GenerateEvent(STEPPER_TIMER, TIM_EventSource_Update);
}

/**
 * @brief PWM测试函数（公共接口）
 * @note 直接输出1kHz，50%占空比的PWM信号，用于验证PWM输出是否正常
 */
void a4988_test_pwm(void)
{
    _a4988_test_pwm();
}

// ====== 新增：步数运动相关变量 ======
/**
 * @brief 电机1步数运动目标步数
 * @note  用于非阻塞步数运动控制，正为正转，负为反转
 */
volatile int32_t stepper1_target_steps = 0; // 目标步数
/**
 * @brief 电机1步数运动起点
 * @note  记录启动步数时的累计步数
 */
volatile int32_t stepper1_start_step = 0;   // 运动起点
/**
 * @brief 电机1步数运动忙标志
 * @note  1表示正在运动，0表示空闲
 */
volatile uint8_t stepper1_busy = 0;         // 是否正在运动

/**
 * @brief 启动电机1步数运动（非阻塞，带自定义速度）
 * @param steps 目标步数（正为正转，负为反转）
 * @param speed 运动速度（RPM）
 * @note 只需调用一次，后续由主循环stepper1_software_tick自动处理到位和停止
 */
void a4988_start_move_steps1_with_speed(int32_t steps, uint16_t speed)
{
    if (steps == 0) return;
    stepper1_start_step = stepper1_state.steps;
    stepper1_target_steps = steps;
    stepper1_busy = 1;
    if (steps > 0) {
        a4988_set_direction(1, 1);
    } else {
        a4988_set_direction(1, 0);
    }
    a4988_enable(1, true);
    a4988_set_speed1(speed); // 使用自定义速度
}

/**
 * @brief 启动电机1步数运动（非阻塞）
 * @param steps 目标步数（正为正转，负为反转）
 * @note 只需调用一次，后续由主循环stepper1_software_tick自动处理到位和停止
 */
void a4988_start_move_steps1(int32_t steps)
{
    a4988_start_move_steps1_with_speed(steps, 60); // 默认60 RPM
}

int32_t degrees_to_steps(int32_t degrees) {
    return (degrees * STEPS_PER_REV) / 360;
}

// ================= 电机2简化版脉冲控制函数 =================

/**
 * @brief 电机2平缓调速脉冲控制 - 核心函数
 * @param pulse_count 目标脉冲数 (0=停止)
 * @param time_ms 完成时间 (毫秒)
 * @param max_freq 最大频率 (Hz)，0=自动计算
 * @note 在指定时间内完成指定脉冲数，使用平缓调速避免电机跟不上
 */
void motor2_smooth_pulses(uint32_t pulse_count, uint32_t time_ms, uint16_t max_freq)
{
    // 停止电机
    if (pulse_count == 0 || time_ms == 0) {
        TIM_Cmd(STEPPER_TIMER, DISABLE);
        motor2_smooth.is_running = 0;
        a4988_enable(2, false);
        return;
    }

    // 计算平均频率：需要在time_ms毫秒内完成pulse_count个脉冲
    float avg_freq = (float)pulse_count * 1000.0f / (float)time_ms;  // Hz

    // 设置频率范围
    motor2_smooth.min_freq = 20;   // 最小频率20Hz，避免过慢
    if (max_freq == 0) {
        // 自动计算最大频率：平均频率的1.5倍，但不超过1500Hz
        motor2_smooth.max_freq = (uint16_t)(avg_freq * 1.5f);
        if (motor2_smooth.max_freq > 1500) motor2_smooth.max_freq = 1500;
        if (motor2_smooth.max_freq < 100) motor2_smooth.max_freq = 100;
    } else {
        motor2_smooth.max_freq = max_freq;
        if (motor2_smooth.max_freq > 1500) motor2_smooth.max_freq = 1500;
    }

    // 设置调速参数
    motor2_smooth.target_pulses = pulse_count;
    motor2_smooth.current_pulses = 0;
    motor2_smooth.current_freq = motor2_smooth.min_freq;  // 从最小频率开始
    motor2_smooth.target_freq = motor2_smooth.max_freq;
    motor2_smooth.accel_step = 5;  // 每次调速增加5Hz
    motor2_smooth.total_time_ms = time_ms;
    motor2_smooth.start_time = Delay_GetTick();  // 记录开始时间
    motor2_smooth.is_running = 1;
    motor2_smooth.phase = 0;  // 开始加速阶段

    // 计算加速和减速的脉冲数（各占总数的25%）
    motor2_smooth.accel_pulses = pulse_count / 4;
    motor2_smooth.decel_start_pulses = pulse_count * 3 / 4;

    // 配置定时器，从最小频率开始
    uint32_t arr_value = (1000000 / motor2_smooth.min_freq) - 1;
    TIM_SetAutoreload(STEPPER_TIMER, arr_value);
    TIM_SetCompare2(STEPPER_TIMER, arr_value / 2);

    // 启动电机
    a4988_enable(2, true);
    TIM_Cmd(STEPPER_TIMER, ENABLE);
}

/**
 * @brief 电机同步更新函数 - 需要在主循环中高频调用
 * @note 根据电机1的进度动态调整电机2的速度
 */
void motors_sync_update(void)
{
    if (!motor_sync.sync_enabled) return;

    static uint32_t last_sync_update = 0;
    uint32_t current_time = Delay_GetTick();

    // 每20ms更新一次同步
    if (current_time - last_sync_update < 20) return;
    last_sync_update = current_time;

    // 获取电机1当前进度
    extern volatile uint8_t stepper1_busy;
    if (!stepper1_busy && !motor2_smooth.is_running) {
        // 两个电机都完成了，禁用同步模式
        motor_sync.sync_enabled = 0;
        return;
    }

    // 计算电机1的进度百分比
    int32_t motor1_moved = stepper1_state.steps - motor_sync.motor1_start_steps;
    float motor1_progress = 0.0f;
    if (motor_sync.motor1_target_steps != 0) {
        motor1_progress = (float)motor1_moved / (float)motor_sync.motor1_target_steps;
        if (motor1_progress < 0) motor1_progress = -motor1_progress;  // 处理负方向
        if (motor1_progress > 1.0f) motor1_progress = 1.0f;
    }

    // 计算电机2应该达到的进度
    uint32_t motor2_should_pulses = (uint32_t)(motor1_progress * motor_sync.motor2_target_pulses);
    uint32_t motor2_actual_pulses = motor2_smooth.current_pulses - motor_sync.motor2_start_pulses;

    // 根据进度差异平缓调整电机2目标速度
    int32_t pulse_diff = motor2_should_pulses - motor2_actual_pulses;
    uint16_t target_freq = motor2_smooth.target_freq;  // 当前目标频率

    if (pulse_diff > 20) {
        // 电机2落后较多，提高目标频率
        target_freq = motor2_smooth.max_freq;
    } else if (pulse_diff > 5) {
        // 电机2轻微落后，适当提高目标频率
        target_freq = (motor2_smooth.max_freq + motor2_smooth.min_freq) / 2 + 50;
        if (target_freq > motor2_smooth.max_freq) target_freq = motor2_smooth.max_freq;
    } else if (pulse_diff < -20) {
        // 电机2超前较多，降低目标频率
        target_freq = motor2_smooth.min_freq;
    } else if (pulse_diff < -5) {
        // 电机2轻微超前，适当降低目标频率
        target_freq = (motor2_smooth.max_freq + motor2_smooth.min_freq) / 2 - 50;
        if (target_freq < motor2_smooth.min_freq) target_freq = motor2_smooth.min_freq;
    } else {
        // 进度基本同步，使用中等频率
        target_freq = (motor2_smooth.max_freq + motor2_smooth.min_freq) / 2;
    }

    // 更新目标频率
    motor2_smooth.target_freq = target_freq;

    // 平缓调整当前频率向目标频率靠近
    if (motor2_smooth.current_freq < motor2_smooth.target_freq) {
        motor2_smooth.current_freq += motor2_smooth.accel_step;  // 使用原有的加速步长
        if (motor2_smooth.current_freq > motor2_smooth.target_freq) {
            motor2_smooth.current_freq = motor2_smooth.target_freq;
        }
    } else if (motor2_smooth.current_freq > motor2_smooth.target_freq) {
        motor2_smooth.current_freq -= motor2_smooth.accel_step;  // 使用原有的减速步长
        if (motor2_smooth.current_freq < motor2_smooth.target_freq) {
            motor2_smooth.current_freq = motor2_smooth.target_freq;
        }
    }

    // 更新电机2的定时器频率
    if (motor2_smooth.is_running) {
        uint32_t arr_value = (1000000 / motor2_smooth.current_freq) - 1;
        TIM_SetAutoreload(STEPPER_TIMER, arr_value);
        TIM_SetCompare2(STEPPER_TIMER, arr_value / 2);
    }

    // 同时调用原有的平缓调速逻辑（处理加速、匀速、减速阶段）
    motor2_smooth_update();
}

/**
 * @brief 电机2平缓调速更新函数 - 需要在主循环中高频调用
 * @note 根据当前阶段和时间动态调整频率
 */
void motor2_smooth_update(void)
{
    if (!motor2_smooth.is_running) return;

    // 如果同步模式启用，跳过独立的平缓调速逻辑
    if (motor_sync.sync_enabled) return;

    static uint32_t last_update_time = 0;
    uint32_t current_time = Delay_GetTick();

    // 每10ms更新一次频率
    if (current_time - last_update_time < 10) return;
    last_update_time = current_time;

    uint32_t elapsed_time = current_time - motor2_smooth.start_time;
    uint32_t current_pulses = motor2_smooth.current_pulses;

    // 根据脉冲数判断当前阶段
    if (current_pulses < motor2_smooth.accel_pulses) {
        // 加速阶段
        motor2_smooth.phase = 0;
        if (motor2_smooth.current_freq < motor2_smooth.max_freq) {
            motor2_smooth.current_freq += motor2_smooth.accel_step;
            if (motor2_smooth.current_freq > motor2_smooth.max_freq) {
                motor2_smooth.current_freq = motor2_smooth.max_freq;
            }
        }
    } else if (current_pulses >= motor2_smooth.decel_start_pulses) {
        // 减速阶段
        motor2_smooth.phase = 2;
        if (motor2_smooth.current_freq > motor2_smooth.min_freq) {
            motor2_smooth.current_freq -= motor2_smooth.accel_step;
            if (motor2_smooth.current_freq < motor2_smooth.min_freq) {
                motor2_smooth.current_freq = motor2_smooth.min_freq;
            }
        }
    } else {
        // 匀速阶段
        motor2_smooth.phase = 1;

        // 时间补偿：如果时间过半但脉冲不足，适当提高频率
        uint32_t expected_pulses = (uint32_t)((float)elapsed_time * motor2_smooth.target_pulses / motor2_smooth.total_time_ms);
        if (current_pulses < expected_pulses * 0.9f) {  // 如果进度落后10%
            if (motor2_smooth.current_freq < motor2_smooth.max_freq) {
                motor2_smooth.current_freq += 2;  // 小幅提速
            }
        } else if (current_pulses > expected_pulses * 1.1f) {  // 如果进度超前10%
            if (motor2_smooth.current_freq > motor2_smooth.min_freq) {
                motor2_smooth.current_freq -= 2;  // 小幅降速
            }
        }
    }

    // 更新定时器频率
    uint32_t arr_value = (1000000 / motor2_smooth.current_freq) - 1;
    TIM_SetAutoreload(STEPPER_TIMER, arr_value);
    TIM_SetCompare2(STEPPER_TIMER, arr_value / 2);
}

/**
 * @brief 检查电机2是否完成脉冲输出
 * @return 1=完成, 0=运行中
 */
uint8_t motor2_is_done(void)
{
    return (motor2_smooth.is_running == 0) ? 1 : 0;
}

/**
 * @brief 获取电机2剩余脉冲数
 * @return 剩余脉冲数
 */
uint32_t motor2_get_remaining(void)
{
    if (motor2_smooth.target_pulses > motor2_smooth.current_pulses) {
        return motor2_smooth.target_pulses - motor2_smooth.current_pulses;
    }
    return 0;
}

/**
 * @brief 获取电机2当前频率
 * @return 当前频率 (Hz)
 */
uint16_t motor2_get_current_freq(void)
{
    return motor2_smooth.current_freq;
}

/**
 * @brief 获取电机2当前阶段
 * @return 0=加速, 1=匀速, 2=减速
 */
uint8_t motor2_get_phase(void)
{
    return motor2_smooth.phase;
}

/**
 * @brief 检查电机同步运动是否完成
 * @return 1=完成, 0=运行中
 */
uint8_t motors_sync_is_done(void)
{
    if (!motor_sync.sync_enabled) return 1;

    extern volatile uint8_t stepper1_busy;
    return (!stepper1_busy && !motor2_smooth.is_running) ? 1 : 0;
}

/**
 * @brief 获取电机同步状态信息
 * @param motor1_progress 电机1进度百分比 (0.0-1.0)
 * @param motor2_progress 电机2进度百分比 (0.0-1.0)
 * @return 1=同步模式运行中, 0=非同步模式
 */
uint8_t motors_get_sync_progress(float *motor1_progress, float *motor2_progress)
{
    if (!motor_sync.sync_enabled) {
        *motor1_progress = 0.0f;
        *motor2_progress = 0.0f;
        return 0;
    }

    // 计算电机1进度
    int32_t motor1_moved = stepper1_state.steps - motor_sync.motor1_start_steps;
    if (motor_sync.motor1_target_steps != 0) {
        *motor1_progress = (float)motor1_moved / (float)motor_sync.motor1_target_steps;
        if (*motor1_progress < 0) *motor1_progress = -*motor1_progress;
        if (*motor1_progress > 1.0f) *motor1_progress = 1.0f;
    } else {
        *motor1_progress = 1.0f;
    }

    // 计算电机2进度
    uint32_t motor2_moved = motor2_smooth.current_pulses - motor_sync.motor2_start_pulses;
    if (motor_sync.motor2_target_pulses != 0) {
        *motor2_progress = (float)motor2_moved / (float)motor_sync.motor2_target_pulses;
        if (*motor2_progress > 1.0f) *motor2_progress = 1.0f;
    } else {
        *motor2_progress = 1.0f;
    }

    return 1;
}

/**
 * @brief 获取电机2同步调速状态
 * @param current_freq 当前频率
 * @param target_freq 目标频率
 * @param pulse_diff 脉冲差异
 * @return 1=同步模式运行中, 0=非同步模式
 */
uint8_t motors_get_sync_speed_info(uint16_t *current_freq, uint16_t *target_freq, int32_t *pulse_diff)
{
    if (!motor_sync.sync_enabled) {
        *current_freq = 0;
        *target_freq = 0;
        *pulse_diff = 0;
        return 0;
    }

    *current_freq = motor2_smooth.current_freq;
    *target_freq = motor2_smooth.target_freq;

    // 计算脉冲差异
    int32_t motor1_moved = stepper1_state.steps - motor_sync.motor1_start_steps;
    float motor1_progress = 0.0f;
    if (motor_sync.motor1_target_steps != 0) {
        motor1_progress = (float)motor1_moved / (float)motor_sync.motor1_target_steps;
        if (motor1_progress < 0) motor1_progress = -motor1_progress;
        if (motor1_progress > 1.0f) motor1_progress = 1.0f;
    }

    uint32_t motor2_should_pulses = (uint32_t)(motor1_progress * motor_sync.motor2_target_pulses);
    uint32_t motor2_actual_pulses = motor2_smooth.current_pulses - motor_sync.motor2_start_pulses;
    *pulse_diff = motor2_should_pulses - motor2_actual_pulses;

    return 1;
}

/**
 * @brief 从RPM转换为每分钟脉冲数
 * @param rpm 转速 (转/分钟)
 * @return 每分钟脉冲数
 */
uint32_t motor2_rpm_to_ppm(uint16_t rpm)
{
    // 每分钟脉冲数 = RPM * 每转步数
    return rpm * STEPS_PER_REV;
}

/**
 * @brief 电机2角度运动 - 平缓调速版
 * @param degrees 目标角度 (正数=正转，负数=反转)
 * @param time_ms 完成时间 (毫秒)
 * @param max_rpm 最大转速 (转/分钟)，0=自动计算
 */
void motor2_move_degrees_smooth(int32_t degrees, uint32_t time_ms, uint16_t max_rpm)
{
    if (degrees == 0 || time_ms == 0) {
        motor2_smooth_pulses(0, 0, 0);  // 停止
        return;
    }

    // 设置方向
    if (degrees > 0) {
        a4988_set_direction(2, 1);  // 正转
    } else {
        a4988_set_direction(2, 0);  // 反转
        degrees = -degrees;         // 转为正数
    }

    // 计算脉冲数：角度 → 脉冲
    uint32_t pulse_count = (degrees * STEPS_PER_REV) / 360;

    // 计算最大频率：RPM → Hz
    uint16_t max_freq = 0;
    if (max_rpm > 0) {
        max_freq = (max_rpm * STEPS_PER_REV) / 60;
    }

    // 启动平缓调速运动
    motor2_smooth_pulses(pulse_count, time_ms, max_freq);
}

/**
 * @brief 启动电机1和电机2的同步运动
 * @param motor1_steps 电机1需要移动的步数
 * @param motor1_speed 电机1的速度 (RPM)
 * @param motor2_degrees 电机2需要移动的角度
 * @note 电机2的速度会自动调整以与电机1同时到达
 */
void motors_start_sync_move(int32_t motor1_steps, uint16_t motor1_speed, int32_t motor2_degrees)
{
    // 禁用同步模式，先停止之前的运动
    motor_sync.sync_enabled = 0;

    if (motor1_steps == 0 && motor2_degrees == 0) {
        // 两个电机都不需要移动
        return;
    }

    // 记录开始状态
    extern volatile int32_t stepper1_state_steps;  // 电机1当前步数
    motor_sync.motor1_start_steps = stepper1_state.steps;
    motor_sync.motor1_target_steps = motor1_steps;
    motor_sync.motor2_start_pulses = motor2_smooth.current_pulses;

    // 计算电机2的脉冲数
    uint32_t motor2_target_pulse_count = 0;
    if (motor2_degrees != 0) {
        // 设置电机2方向
        if (motor2_degrees > 0) {
            a4988_set_direction(2, 1);  // 正转
        } else {
            a4988_set_direction(2, 0);  // 反转
            motor2_degrees = -motor2_degrees;  // 转为正数
        }
        motor2_target_pulse_count = (motor2_degrees * STEPS_PER_REV) / 360;
    }
    motor_sync.motor2_target_pulses = motor2_target_pulse_count;

    // 启动电机1
    if (motor1_steps != 0) {
        a4988_start_move_steps1_with_speed(motor1_steps, motor1_speed);
    }

    // 启动电机2（使用同步优化的平缓调速参数）
    if (motor2_target_pulse_count > 0) {
        // 估算电机1的运动时间
        uint32_t abs_steps1 = (motor1_steps > 0) ? motor1_steps : -motor1_steps;
        uint32_t estimated_time_ms = (abs_steps1 * 60 * 1000) / (motor1_speed * STEPS_PER_REV);

        // 启动电机2的平缓调速，使用保守的最大频率
        uint16_t max_freq = (motor1_speed * STEPS_PER_REV) / 60;  // 与电机1相同的基础频率
        if (max_freq > 1200) max_freq = 1200;  // 限制最大频率，确保电机能跟上

        motor2_smooth_pulses(motor2_target_pulse_count, estimated_time_ms, max_freq);

        // 设置同步专用的平缓调速参数
        motor2_smooth.accel_step = 3;  // 降低加速步长，更平缓
        motor2_smooth.min_freq = 30;   // 提高最小频率，避免过慢

        // 初始目标频率设为中等值
        motor2_smooth.target_freq = (motor2_smooth.max_freq + motor2_smooth.min_freq) / 2;
    }

    // 启用同步模式
    motor_sync.sync_enabled = 1;
    motor_sync.sync_start_time = Delay_GetTick();
}

/**
 * @brief 电机2角度运动 - 兼容旧版本
 * @param degrees 目标角度 (正数=正转，负数=反转)
 * @param rpm 转速 (转/分钟)
 */
void motor2_move_degrees(int32_t degrees, uint16_t rpm)
{
    // 计算运动时间：根据角度和转速估算
    uint32_t pulse_count = (degrees > 0 ? degrees : -degrees) * STEPS_PER_REV / 360;
    uint32_t time_ms = (pulse_count * 60 * 1000) / (rpm * STEPS_PER_REV);

    // 使用平缓调速，最大转速为指定RPM的1.2倍
    motor2_move_degrees_smooth(degrees, time_ms, rpm * 120 / 100);
}
