#ifndef VL53L0X_H
#define VL53L0X_H

#include <stdbool.h>
#include <stdint.h>

#define VL53L0X_OUT_OF_RANGE (8190) // 超出测量范围的返回值

// Comment out these definitions if not connected
// #define VL53L0X_SECOND // 注释掉多传感器支持
// #define VL53L0X_THIRD  // 注释掉多传感器支持

// 枚举：VL53L0X 传感器索引
// 用于区分多个传感器
// Enum for sensor index, used to select among multiple sensors
typedef enum
{
    VL53L0X_IDX_FIRST, // 
#ifdef VL53L0X_SECOND
    VL53L0X_IDX_SECOND, // 第二个传感器
#endif
#ifdef VL53L0X_THIRD
    VL53L0X_IDX_THIRD,  // 第三个传感器
#endif
} vl53l0x_idx_t;

// 测量模式枚举
typedef enum
{
    VL53L0X_MODE_DEFAULT,      // 默认模式
    VL53L0X_MODE_HIGH_SPEED,   // 高速模式
    VL53L0X_MODE_HIGH_ACCURACY // 高精度模式
} vl53l0x_mode_t;

/**
 * Initialize sensors in vl53l0x_idx_t enum.
 * @note Each sensor must have its XSHUT pin connected.
 * 初始化所有枚举中的VL53L0X传感器。
 * 注意：每个传感器都必须连接XSHUT引脚。
 */
bool vl53l0x_init(void);

/**
 * Set measurement mode for better accuracy
 * @param idx Sensor index
 * @param mode Measurement mode
 * @return True on success, False on failure
 * 设置测量模式以提高精度
 */
bool vl53l0x_set_mode(vl53l0x_idx_t idx, vl53l0x_mode_t mode);

/**
 * Perform single range measurement
 * @param idx Select specific sensor
 * @param range Contains measured distance or VL53L0X_OUT_OF_RANGE if out of range.
 * @return True on success, False on failure
 * @note   Polling based
 * 对指定传感器进行一次测距
 * @param idx 选择要测量的传感器
 * @param range 测量结果（单位：毫米），超出范围时为VL53L0X_OUT_OF_RANGE
 * @return 成功返回true，失败返回false
 * @note   轮询方式
 */
bool vl53l0x_read_range_single(vl53l0x_idx_t idx, uint16_t *range);

/**
 * Perform multiple measurements and return average for better accuracy
 * @param idx Sensor index
 * @param range Average distance result
 * @param samples Number of samples to average (1-10)
 * @return True on success, False on failure
 * 进行多次测量并返回平均值以提高精度
 * @param idx 传感器索引
 * @param range 平均距离结果
 * @param samples 采样次数 (1-10)
 * @return 成功返回true，失败返回false
 */
bool vl53l0x_read_range_average(vl53l0x_idx_t idx, uint16_t *range, uint8_t samples);

/**
 * High precision measurement with outlier filtering
 * @param idx Sensor index
 * @param range Filtered average distance result
 * @param samples Number of samples (5-20 recommended)
 * @return True on success, False on failure
 * 高精度测量，带异常值过滤
 * @param idx 传感器索引
 * @param range 过滤后的平均距离结果
 * @param samples 采样次数 (推荐5-20次)
 * @return 成功返回true，失败返回false
 */
bool vl53l0x_read_range_high_precision(vl53l0x_idx_t idx, uint16_t *range, uint8_t samples);

/**
 * Adaptive precision measurement based on measurement stability
 * @param idx Sensor index
 * @param range Adaptive precision result
 * @param target_accuracy Target accuracy in mm (1-5mm)
 * @return True on success, False on failure
 * 自适应精度测量，基于测量稳定性
 * @param idx 传感器索引
 * @param range 自适应精度结果
 * @param target_accuracy 目标精度（毫米，1-5mm）
 * @return 成功返回true，失败返回false
 */
bool vl53l0x_read_range_adaptive(vl53l0x_idx_t idx, uint16_t *range, uint8_t target_accuracy);

/**
 * Continuous measurement for real-time applications
 * @param idx Sensor index
 * @param range Current distance result
 * @param filter_enabled Enable low-pass filtering
 * @return True on success, False on failure
 * 连续测量模式，适用于实时应用
 * @param idx 传感器索引
 * @param range 当前距离结果
 * @param filter_enabled 是否启用低通滤波
 * @return 成功返回true，失败返回false
 */
bool vl53l0x_read_range_continuous(vl53l0x_idx_t idx, uint16_t *range, bool filter_enabled);

#endif /* VL53L0X_H */
