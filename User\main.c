/**
 * =====================================================================================
 * @file    main.c
 * @brief   主程序文件
 * <AUTHOR>
 * @date    (日期)
 * @version 1.0
 *
 * @note
 *   - 本程序集成了A4988步进电机驱动、VL53L0X激光测距传感器和USART串口通信。
 *   - 通过串口发送指令可以控制电机转速、读取距离、执行校准等。
 *   - 距离校准值支持掉电保持功能（存储在Flash中）。
 * =====================================================================================
 */

#include "stm32f10x.h"
#include "a4988.h"
#include "Delay.h"
#include "i2c.h"
#include "vl53l0x.h"
#include "usart.h"
#include "storage.h"
#include "Key.h"
#include "OLED.h"

#define DIR_POSITIVE 0   // 正转方向
#define DIR_NEGATIVE 1   // 反转方向

// ================== 角度记录结构体 ==================
/**
 * @brief 记录一对电机角度
 * @note  用于自动流程和回放流程中记录每次检测到的电机1和电机2的角度
 */
typedef struct {
    int32_t angle1; // 电机1角度
    int32_t angle2; // 电机2角度
} AngleRecord;

// 全局数组，用于存储20对角度数据
AngleRecord recorded_data[20] = {0}; // 记录每轮检测的角度

// ================== 状态机定义 ==================
/**
 * @brief 自动运行和回放流程的状态机枚举
 * @note  用于主流程控制自动检测、回放、归零等各个阶段
 */
typedef enum {
    AUTO_IDLE,                // 空闲状态
    AUTO_START,               // 自动流程开始
    AUTO_MOTOR2_FORWARD,      // 电机2正向运动
    AUTO_MOTOR2_FORWARD_WAIT, // 等待电机2正向运动完成
    AUTO_MOTOR2_BACK,         // 电机2归零
    AUTO_MOTOR2_BACK_WAIT,    // 等待电机2归零完成
    AUTO_MOTOR1_FORWARD,      // 电机1正向运动
    AUTO_MOTOR1_FORWARD_WAIT, // 等待电机1正向运动完成
    AUTO_DONE,                // 自动流程全部完成
    // 新增回放相关状态
    AUTO_REPLAY_START = 100,  // 回放流程开始
    AUTO_REPLAY_ZERO,        // 回放前归零
    AUTO_REPLAY_ZERO_WAIT,   // 等待归零完成
    AUTO_REPLAY_MOVE,         // 两电机运动到目标
    AUTO_REPLAY_WAIT,         // 等待到位
    AUTO_REPLAY_DONE          // 回放完成
} AutoRunState;

// ================== 全局变量 ==================
static AutoRunState autoRunState = AUTO_IDLE; // 当前状态机状态
static int autoMotor1Rounds = 0;              // 自动流程轮次计数
static int32_t angle1 = 0, angle2 = 0;        // 当前角度
static uint16_t distance = 0;                 // 当前距离
static int32_t step_count = 0;                // 记录电机2正向运动步数
// 回放流程相关变量
static int replay_index = 0;                  // 回放当前索引
static int32_t target_angle1 = 0;             // 回放目标角度1
static int32_t target_angle2 = 0;             // 回放目标角度2

/**
 * @brief 通过串口发送一个整数（正数或负数）。
 * @param n 要打印的整数。
 * @retval 无
 */
void print_number(int32_t n) {
    char buf[12];
    int i = 0;
    if (n == 0) {
        usartSendByte('0');
        return;
    }
    if (n < 0) {
        usartSendByte('-');
        n = -n;
    }
    while(n > 0) {
        buf[i++] = (n % 10) + '0';
        n /= 10;
    }
    for (int j = i - 1; j >= 0; j--) {
        usartSendByte(buf[j]);
    }

    
}

/**
 * @brief 自动运行和回放流程的主状态机
 * @note 需在主循环中高频率调用
 * @note 主要分为：空闲、自动流程、回退、回放等阶段
 */
void auto_run_state_machine(void) {
    static uint8_t first_entry = 1;
    static uint32_t autoTimer = 0;
    switch (autoRunState) {
        case AUTO_IDLE:
            // 空闲状态，等待触发
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            angle1 = a4988_get_angle(1);
            angle2 = a4988_get_angle(2);
            OLED_Clear();
            OLED_ShowString(0, 0, "IDLE", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            OLED_Update();
            break;
        case AUTO_START:
            // 自动流程开始，初始化轮次和角度记录
            // 新增：开始前将两个电机角度归零
            a4988_reset_angle(1); // 电机1角度归零
            a4988_reset_angle(2); // 电机2角度归零
            autoMotor1Rounds = 0;
            for (int i = 0; i < 20; i++) {
                recorded_data[i].angle1 = 0;
                recorded_data[i].angle2 = 0;
            }
            usartSendString("自动流程开始...\r\n");
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            angle1 = a4988_get_angle(1);
            angle2 = a4988_get_angle(2);
            OLED_Clear();
            OLED_ShowString(0, 0, "AUTO START", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            OLED_Update();
            autoRunState = AUTO_MOTOR2_FORWARD;
            break;
        case AUTO_MOTOR2_FORWARD:
            // 使用新的同步控制：电机2单独运动360度
            motors_start_sync_move(0, 100, 360);  // 电机1不动，电机2转360度
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            angle1 = a4988_get_angle(1);
            angle2 = a4988_get_angle(2);
            OLED_Clear();
            OLED_ShowString(0, 0, "M2 FORWARD", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            OLED_Update();
            autoRunState = AUTO_MOTOR2_FORWARD_WAIT;
            break;
        case AUTO_MOTOR2_FORWARD_WAIT:
            // 使用新的同步控制等待
            angle2 = a4988_get_angle(2);
            angle1 = a4988_get_angle(1);
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            OLED_Clear();
            OLED_ShowString(0, 0, "M2 FWD WAIT", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            OLED_Update();
            if (distance > 200 && angle2 > 2) {
                usartSendString("检测成功! 当前步数: ");
                int32_t current_steps = stepper2_pulse_count;
                int32_t back_steps = -current_steps;
                print_number(current_steps);
                usartSendString(", 回退步数: ");
                print_number(back_steps);
                usartSendString("\r\n");
                if (autoMotor1Rounds < 20) {
                    recorded_data[autoMotor1Rounds].angle1 = angle1;
                    recorded_data[autoMotor1Rounds].angle2 = angle2;
                }
                // 停止当前运动，准备回退
                autoRunState = AUTO_MOTOR2_BACK;
            }
            break;
        case AUTO_MOTOR2_BACK: {
            // 使用新的同步控制回退到0度
            int32_t current_angle = a4988_get_angle(2);
            int32_t back_degrees = -current_angle;  // 回到0度
            usartSendString("启动回退运动，角度: ");
            print_number(back_degrees);
            usartSendString("\r\n");
            motors_start_sync_move(0, 100, back_degrees);  // 电机1不动，电机2回退
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            angle1 = a4988_get_angle(1);
            angle2 = a4988_get_angle(2);
            OLED_Clear();
            OLED_ShowString(0, 0, "M2 BACK", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            OLED_Update();
            autoRunState = AUTO_MOTOR2_BACK_WAIT;
            break;
        }
        
        case AUTO_MOTOR2_BACK_WAIT: {
            // 这里会不断调用stepper2_acceleration_tick()，实现电机2的加减速和步数到位检测
            // 如果你想让回退过程“慢慢减速”，请在a4988.c的stepper2_acceleration_tick函数里实现
            // 例如：根据剩余步数动态调整stepper2_state.target_rpm，剩余步数越少，target_rpm越低
            // 使用新的同步控制，不需要调用stepper2_acceleration_tick()

            // 下面是OLED和状态切换，不需要改动
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            angle1 = a4988_get_angle(1);
            angle2 = a4988_get_angle(2);
            OLED_Clear();
            OLED_ShowString(0, 0, "M2 BACK WAIT", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            OLED_Update();
            if (motors_sync_is_done()) {
                a4988_reset_angle(2);  // 角度归零
                usartSendString("电机2已角度归零\r\n");
                autoRunState = AUTO_MOTOR1_FORWARD;
            }
            break;
        }
        case AUTO_MOTOR1_FORWARD:
            // 电机1精确步数控制，走一圈（STEPS_PER_REV步）
            a4988_start_move_steps1(STEPS_PER_REV); // 精确走一圈
            autoRunState = AUTO_MOTOR1_FORWARD_WAIT;
            break;
        case AUTO_MOTOR1_FORWARD_WAIT:
            stepper1_software_tick();
            // 等待电机1运动完成
            if (!stepper1_busy) {
                a4988_set_speed1(0);
                a4988_enable(1, false);
                usartSendString("电机1完成一圈\r\n");
                autoMotor1Rounds++;
                if (autoMotor1Rounds < 20) {
                    autoRunState = AUTO_MOTOR2_FORWARD;
                } else {
                    autoRunState = AUTO_DONE;
                }
            }
            break;
        case AUTO_DONE:
            usartSendString("全部20次流程完成！\r\n");
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            angle1 = a4988_get_angle(1);
            angle2 = a4988_get_angle(2);
            OLED_Clear();
            OLED_ShowString(0, 0, "AUTO DONE", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            // 新增：OLED显示已存储点数
            OLED_ShowString(80, 0, "Pts:", OLED_8X16);
            OLED_ShowNum(120, 0, autoMotor1Rounds, 2, OLED_8X16);
            OLED_Update();
            usartSendString("\r\n--- 角度记录结果 ---\r\n");
            for (int i = 0; i < 20; i++) {
                usartSendString("电机1: ");
                print_number(recorded_data[i].angle1);
                usartSendString("度, 电机2: ");
                print_number(recorded_data[i].angle2);
                usartSendString("度\r\n");
            }
            usartSendString("----------------------\r\n");
            autoRunState = AUTO_IDLE;
            break;
   
   
   
   
   
            // ================== 回放流程 ==================
        case AUTO_REPLAY_START:
            // 回放流程开始，初始化回放索引replay_index为0
            // OLED和串口显示回放开始信息
            replay_index = 0;
            usartSendString("回放流程开始...\r\n");
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            angle1 = a4988_get_angle(1);
            angle2 = a4988_get_angle(2);
            OLED_Clear();
            OLED_ShowString(0, 0, "REPLAY START", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            OLED_Update();
            autoRunState = AUTO_REPLAY_ZERO;
            break;

        case AUTO_REPLAY_ZERO: {
            int32_t angle1_deg = a4988_get_angle(1);
            int32_t angle2_deg = a4988_get_angle(2);
            int32_t steps1 = degrees_to_steps(angle1_deg);
            int32_t steps2 = degrees_to_steps(angle2_deg);
            a4988_start_move_steps1(-steps1);
            a4988_start_move_steps2(-steps2);
            OLED_Clear();
            OLED_ShowString(0, 0, "REPLAY ZERO", OLED_8X16);
            OLED_ShowString(0, 16, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 16, angle1_deg, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle2_deg, 5, OLED_8X16);
            OLED_Update();
            autoRunState = AUTO_REPLAY_ZERO_WAIT;
            break;
        }
        case AUTO_REPLAY_ZERO_WAIT: {
            // 等待两个电机都归零到位
            stepper1_software_tick();
            stepper2_acceleration_tick();
            extern volatile uint8_t stepper1_busy;
            extern volatile uint8_t stepper2_busy;
            int32_t angle1 = a4988_get_angle(1);
            int32_t angle2 = a4988_get_angle(2);
            OLED_Clear();
            OLED_ShowString(0, 0, "ZERO WAIT", OLED_8X16);
            OLED_ShowString(0, 16, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 16, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle2, 5, OLED_8X16);
            OLED_Update();
            if (!stepper1_busy && !stepper2_busy) {
                a4988_reset_angle(1); // 步数清零
                a4988_reset_angle(2);
                autoRunState = AUTO_REPLAY_MOVE;
            }
            break;
        }
        case AUTO_REPLAY_MOVE:
            if (replay_index < 20 && (recorded_data[replay_index].angle1 != 0 || recorded_data[replay_index].angle2 != 0)) {
                target_angle1 = recorded_data[replay_index].angle1;
                target_angle2 = recorded_data[replay_index].angle2;
                int32_t current_angle1 = a4988_get_angle(1);
                int32_t current_angle2 = a4988_get_angle(2);
                int32_t delta_steps1 = degrees_to_steps(target_angle1 - current_angle1);
                int32_t delta_steps2 = degrees_to_steps(target_angle2 - current_angle2);
                
                // 计算同步速度 - 让两个电机同时到达
                int32_t abs_steps1 = delta_steps1 > 0 ? delta_steps1 : -delta_steps1;
                int32_t abs_steps2 = delta_steps2 > 0 ? delta_steps2 : -delta_steps2;
                
                uint16_t speed1, speed2;
                if (abs_steps1 == 0 && abs_steps2 == 0) {
                    // 两个电机都不需要移动
                    speed1 = speed2 = 100;
                } else if (abs_steps1 == 0) {
                    // 只有电机2移动
                    speed1 = 100;
                    speed2 = 400;  // 提高电机2单独运行速度
                } else if (abs_steps2 == 0) {
                    // 只有电机1移动
                    speed1 = 200;  // 提高电机1单独运行速度
                    speed2 = 400;
                } else {
                    // 两个电机都需要移动，计算同步速度
                    if (abs_steps1 >= abs_steps2) {
                        // 电机1移动距离更长，电机1用较快速度，电机2同步
                        speed1 = 200;  // 提高电机1最大速度
                        speed2 = (uint16_t)((400L * abs_steps2) / abs_steps1);
                        if (speed2 < 80) speed2 = 80;  // 提高最小速度限制，避免低速共振
                    } else {
                        // 电机2移动距离更长，电机2用较快速度，电机1同步
                        speed2 = 400;  // 大幅提高电机2最大速度
                        speed1 = (uint16_t)((200L * abs_steps1) / abs_steps2);
                        if (speed1 < 60) speed1 = 60;  // 提高最小速度限制，避免低速共振
                    }
                }
                
                // 使用同步控制：电机2自动调整速度跟踪电机1
                int32_t delta_degrees2 = (delta_steps2 * 360) / STEPS_PER_REV;

                // 启动同步运动
                motors_start_sync_move(delta_steps1, speed1, delta_degrees2);
                
                vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
                angle1 = current_angle1;
                angle2 = current_angle2;
                // 中文串口输出当前回放点信息
                usartSendString("【回放移动】点位:");
                print_number(replay_index + 1);
                usartSendString(" 电机1: ");
                print_number(angle1);
                usartSendString(" -> ");
                print_number(target_angle1);
                usartSendString(" 速度: ");
                print_number(speed1);
                usartSendString(" 电机2: ");
                print_number(angle2);
                usartSendString(" -> ");
                print_number(target_angle2);
                usartSendString(" 速度: ");
                print_number(speed2);
                usartSendString("\r\n");
                OLED_Clear();
                OLED_ShowString(0, 0, "REPLAY MOVE", OLED_8X16);
                OLED_ShowString(0, 16, "d:", OLED_8X16);
                OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
                OLED_ShowString(0, 32, "a1:", OLED_8X16);
                OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
                OLED_ShowString(80, 32, "->", OLED_8X16);
                OLED_ShowSignedNum(104, 32, target_angle1, 5, OLED_8X16);
                OLED_ShowString(0, 48, "a2:", OLED_8X16);
                OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
                OLED_ShowString(80, 48, "->", OLED_8X16);
                OLED_ShowSignedNum(104, 48, target_angle2, 5, OLED_8X16);
                // 新增：OLED显示当前回放到第几个点
                OLED_ShowString(80, 0, "Pt:", OLED_8X16);
                OLED_ShowNum(112, 0, replay_index + 1, 2, OLED_8X16);
                OLED_Update();
                autoRunState = AUTO_REPLAY_WAIT;
            } else {
                // 所有点回放完成，进入回放完成状态
                autoRunState = AUTO_REPLAY_DONE;
            }
            break;
        case AUTO_REPLAY_WAIT:
            // 等待电机1和电机2都运动到目标点
            // 到位后replay_index++，进入下一个点的回放
            // 注意：电机更新函数已在主循环中调用，这里不需要重复调用
            extern volatile uint8_t stepper1_busy;
            extern volatile uint8_t stepper2_busy;
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            angle1 = a4988_get_angle(1);
            angle2 = a4988_get_angle(2);
            // 中文串口输出当前回放点信息和同步状态
            usartSendString("【回放等待】点位:");
            print_number(replay_index + 1);
            usartSendString(" 电机1: ");
            print_number(angle1);
            usartSendString(" -> ");
            print_number(recorded_data[replay_index].angle1);
            usartSendString(" 电机2: ");
            print_number(angle2);
            usartSendString(" -> ");
            print_number(recorded_data[replay_index].angle2);

            // 显示同步进度和调速信息
            float motor1_progress, motor2_progress;
            uint16_t current_freq, target_freq;
            int32_t pulse_diff;

            if (motors_get_sync_progress(&motor1_progress, &motor2_progress)) {
                usartSendString(" 同步: M1=");
                print_number((int)(motor1_progress * 100));
                usartSendString("% M2=");
                print_number((int)(motor2_progress * 100));
                usartSendString("%");

                // 显示调速信息
                if (motors_get_sync_speed_info(&current_freq, &target_freq, &pulse_diff)) {
                    usartSendString(" Freq:");
                    print_number(current_freq);
                    usartSendString("->");
                    print_number(target_freq);
                    usartSendString("Hz Diff:");
                    if (pulse_diff >= 0) {
                        usartSendString("+");
                    }
                    print_number(pulse_diff);
                }
            }
            usartSendString("\r\n");
            OLED_Clear();
            OLED_ShowString(0, 0, "REPLAY WAIT", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(80, 32, "->", OLED_8X16);
            OLED_ShowSignedNum(104, 32, recorded_data[replay_index].angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            OLED_ShowString(80, 48, "->", OLED_8X16);
            OLED_ShowSignedNum(104, 48, recorded_data[replay_index].angle2, 5, OLED_8X16);
            // 新增：OLED显示当前回放到第几个点
            OLED_ShowString(80, 0, "Pt:", OLED_8X16);
            OLED_ShowNum(112, 0, replay_index + 1, 2, OLED_8X16);
            OLED_Update();
            // 检查同步运动是否完成
            if (motors_sync_is_done()) {
                replay_index++;
                autoRunState = AUTO_REPLAY_MOVE;
            }
            break;
        case AUTO_REPLAY_DONE:
            // 回放流程完成，OLED和串口显示回放完成信息，状态机回到空闲
            usartSendString("回放流程完成！\r\n");
            vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &distance);
            angle1 = a4988_get_angle(1);
            angle2 = a4988_get_angle(2);
            OLED_Clear();
            OLED_ShowString(0, 0, "REPLAY DONE", OLED_8X16);
            OLED_ShowString(0, 16, "d:", OLED_8X16);
            OLED_ShowNum(32, 16, distance, 5, OLED_8X16);
            OLED_ShowString(0, 32, "a1:", OLED_8X16);
            OLED_ShowSignedNum(32, 32, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 48, "a2:", OLED_8X16);
            OLED_ShowSignedNum(32, 48, angle2, 5, OLED_8X16);
            OLED_Update();
            autoRunState = AUTO_IDLE;
            break;
    }
}

int main(void)
{
    /* ==================== 初始化系统模块 ==================== */
    a4988_init();      // 初始化A4988电机驱动模块
    Delay_Init();      // 初始化延时函数
    i2c_init();        // 初始化I2C总线（用于VL53L0X）
    storage_init();    // 初始化Flash存储模块
    Key_Init();        // 初始化按键模块
    
    /* ==================== 初始化USART串口 ==================== */
    UsartConfig_t usartConfig = {
        .baudRate = 115200,    // 波特率
        .dataBits = 8,         // 数据位
        .stopBits = 1,         // 停止位
        .parity = 0,           // 无校验
        .flowControl = 0       // 无流控
    };
    
    if (usartInit(&usartConfig) == USART_OK) {
        // 发送初始化完成消息
        usartSendString("=== 系统启动 ===\r\n");
        usartSendString("STM32F103 串口通信 + 电机控制 + 距离检测\r\n");
        usartSendString("波特率: 115200, 格式: 8N1\r\n");
        usartSendString("开始自动检测程序...\r\n");
        usartSendString("========================\r\n");
    }
    
    /* ==================== 自动检测程序 ==================== */
    usartSendString("\r\n");
    usartSendString("========== 自动检测程序开始 ==========\r\n");
    
    // 1. 检测I2C通信
    usartSendString("1. 检测I2C通信...\r\n");
    i2c_set_slave_address(0x29); // VL53L0X的默认I2C地址
    uint8_t device_id = 0;
    if (i2c_read_addr8_data8(0xC0, &device_id)) {
        usartSendString("   I2C通信正常，设备ID: 0x");
        uint8_t high = (device_id >> 4) & 0x0F;
        uint8_t low = device_id & 0x0F;
        usartSendByte(high < 10 ? '0' + high : 'A' + high - 10);
        usartSendByte(low < 10 ? '0' + low : 'A' + low - 10);
        usartSendString(" (期望值: 0xEE)\r\n");
        if (device_id == 0xEE) {
            usartSendString("   ✓ VL53L0X设备识别成功\r\n");
        } else {
            usartSendString("   ⚠ 设备ID不匹配，可能连接错误\r\n");
        }
    } else {
        usartSendString("   ✗ I2C通信失败，请检查连接\r\n");
    }
    
    // 2. 检测VL53L0X传感器
    usartSendString("2. 检测VL53L0X距离传感器...\r\n");
    if (vl53l0x_init()) {
        usartSendString("   ✓ VL53L0X传感器初始化成功\r\n");
        
        // 进行3次距离测量测试
        usartSendString("   进行距离测量测试 (3次):\r\n");
        uint32_t total_distance = 0;
        uint8_t valid_count = 0;
        
        for (int i = 0; i < 3; i++) {
            uint16_t test_distance;
            usartSendString("   测量 ");
            usartSendByte('0' + (i + 1));
            usartSendString(": ");
            
            if (vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &test_distance)) {
                if (test_distance == VL53L0X_OUT_OF_RANGE) {
                    usartSendString("超出范围\r\n");
                } else {
                    print_number(test_distance);
                    usartSendString(" mm\r\n");
                    total_distance += test_distance;
                    valid_count++;
                }
            } else {
                usartSendString("读取失败\r\n");
            }
            Delay_ms(100);
        }
        
        if (valid_count > 0) {
            uint16_t avg_distance = (uint16_t)(total_distance / valid_count);
            usartSendString("   平均距离: ");
            print_number(avg_distance);
            usartSendString(" mm\r\n");
            usartSendString("   ✓ 距离传感器工作正常\r\n");
        } else {
            usartSendString("   ✗ 距离传感器测试失败\r\n");
        }
    } else {
        usartSendString("   ✗ VL53L0X传感器初始化失败\r\n");
        usartSendString("   请检查:\r\n");
        usartSendString("   - I2C连接 (SDA: PB7, SCL: PB6)\r\n");
        usartSendString("   - XSHUT引脚连接 (PB0)\r\n");
        usartSendString("   - 电源连接 (3.3V, GND)\r\n");
    }
    
    // 3. 检测电机系统
    usartSendString("3. 检测电机系统...\r\n");
    a4988_set_direction(1, 1);  // 设置电机1方向
    a4988_enable(1, true);      // 使能电机1
    a4988_set_direction(2, 1);  // 设置电机2方向
    a4988_enable(2, true);      // 使能电机2
    
    usartSendString("   ✓ 电机1和电机2已使能\r\n");
    usartSendString("   电机1当前角度: ");
    print_number(a4988_get_angle(1));
    usartSendString(" 度\r\n");
    usartSendString("   电机2当前角度: ");
    print_number(a4988_get_angle(2));
    usartSendString(" 度\r\n");
    
    // 4. 检测存储系统
    usartSendString("4. 检测存储系统...\r\n");
    int16_t distance_offset = storage_read_offset();
    usartSendString("   当前校准偏移量: ");
    print_number(distance_offset);
    usartSendString(" mm\r\n");
    usartSendString("   ✓ Flash存储系统正常\r\n");
    
    // 5. 检测串口通信
    usartSendString("5. 检测串口通信...\r\n");
    usartSendString("   发送测试消息: Hello World!\r\n");
    usartSendString("   ✓ 串口通信正常\r\n");
    
    /* ==================== 定义主循环变量 ==================== */
    uint8_t receivedData;                           // 用于存储从串口接收到的单个字节
    uint32_t counter = 0;                           // 通用计数器，用于定时任务
    uint16_t distance = 0;                          // 用于存储处理后的距离值
    
    // OLED初始化
    OLED_Init();
    OLED_Clear();
    OLED_ShowString(0, 0, "系统启动...", OLED_8X16);
    OLED_Update();
    Delay_ms(1000);
    OLED_Clear();
    OLED_Update();
    
    // 打印当前生效的校准值
    usartSendString("当前校准偏移量: ");
    print_number(distance_offset);
    usartSendString(" mm\r\n");
    
    // 电机2上电后默认停止并禁用，防止角度无故增加
    a4988_set_speed2(0);   // 停止电机2
    a4988_enable(2, false); // 禁用电机2
    
    while(1)
    {
        // 自动流程和回放流程状态机
        auto_run_state_machine();

        // 电机运动处理 - 统一使用新的同步控制系统
        stepper1_software_tick();
        motors_sync_update();  // 统一使用同步控制
        
        // 其他OLED显示任务（如主界面）
        if (autoRunState == AUTO_IDLE) {
            // 只在空闲时显示主界面
            int32_t angle1 = a4988_get_angle(1);
            int32_t angle2 = a4988_get_angle(2);
            uint16_t raw_distance = 0;
            uint16_t show_distance = 0;
            if (vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &raw_distance)) {
                show_distance = raw_distance;
            } else {
                show_distance = 0xFFFF;
            }
            OLED_Clear();
            OLED_ShowString(0, 0, "m1:", OLED_8X16);
            OLED_ShowSignedNum(48, 0, angle1, 5, OLED_8X16);
            OLED_ShowString(0, 2*8, "m2:", OLED_8X16);
            OLED_ShowSignedNum(48, 2*8, angle2, 5, OLED_8X16);
            OLED_ShowString(0, 4*8, "d:", OLED_8X16);
            if (show_distance == 0xFFFF) {
                OLED_ShowString(48, 4*8, "Err", OLED_8X16);
            } else {
                OLED_ShowNum(48, 4*8, show_distance, 5, OLED_8X16);
                OLED_ShowString(88, 4*8, "mm", OLED_8X16);
            }
            // 新增：OLED显示当前已存储点数
            OLED_ShowString(80, 0, "Pts:", OLED_8X16);
            OLED_ShowNum(120, 0, autoMotor1Rounds, 2, OLED_8X16);
            OLED_Update();
        }
        
        /* ==================== 定时任务：状态上报 ==================== */
        // 每隔一段时间（500 * 10ms = 5秒），自动上报一次系统状态和校准后的距离
        if (counter % 500 == 0) {
            usartSendString("系统状态: 运行中\r\n");
            
            // 读取距离数据（单次测量）
            uint16_t raw_distance;
            if (vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &raw_distance)) {
                if (raw_distance == VL53L0X_OUT_OF_RANGE) {
                    usartSendString("距离: 超出范围 (>2000mm)\r\n");
                } else {
                    // 应用校准偏移量
                    distance = (raw_distance > distance_offset) ? (raw_distance - distance_offset) : 0;
                    usartSendString("距离 (已校准): ");
                    print_number(distance);
                    usartSendString(" mm\r\n");
                }
            } else {
                usartSendString("距离读取失败\r\n");
            }
        }
        
        /* ==================== 按键检测任务 ==================== */
        uint8_t keyNum = Key_GetNum();
        if (keyNum == 1) {
            usartSendString("按下按键1，启动自动流程\r\n");
            autoRunState = AUTO_START;
        }
        if (keyNum == 2) {
            usartSendString("按下按键2，启动回放流程\r\n");
            autoRunState = AUTO_REPLAY_START;
        }
        
        /* ==================== 轮询任务：处理串口指令 ==================== */
        if (usartGetReceivedByte(&receivedData)) {
            // 回显接收到的数据
            usartSendString("收到: ");
            usartSendByte(receivedData);
            usartSendString(" (0x");
            
            // 发送十六进制值
            uint8_t high = (receivedData >> 4) & 0x0F;
            uint8_t low = receivedData & 0x0F;
            usartSendByte(high < 10 ? '0' + high : 'A' + high - 10);
            usartSendByte(low < 10 ? '0' + low : 'A' + low - 10);
            usartSendString(")\r\n");
            
            // 根据接收到的命令执行不同操作
            switch (receivedData) {
                /* --- 电机控制指令 --- */
                case 'a':
                    usartSendString("收到自动运行命令，开始自动流程\r\n");
                    // 进入自动运行前，先将两个电机角度归零
                    a4988_reset_angle(1); // Motor 1 angle reset to 0
                    a4988_reset_angle(2); // Motor 2 angle reset to 0
                    auto_run_state_machine();
                    break;
                /* --- 传感器与系统指令 --- */
                case 'v': { // 'v' - View: 查看记录的角度数组
                    usartSendString("--- 上一次记录的角度 ---\r\n");
                    for (int i = 0; i < 20; i++) {
                        usartSendString("电机1: ");
                        print_number(recorded_data[i].angle1);
                        usartSendString("度, 电机2: ");
                        print_number(recorded_data[i].angle2);
                        print_number(recorded_data[i].angle2);
                        usartSendString("度\r\n");
                    }
                    usartSendString("-------------------------\r\n");
                    break;
                }
                case 'g': { // 'g' - Get Angles: 获取两个电机的当前角度
                    usartSendString("--- 当前电机角度 ---\r\n");
                    // 获取并打印电机1的角度
                    int32_t angle1 = a4988_get_angle(1);
                    usartSendString("  电机1: ");
                    print_number(angle1);
                    usartSendString(" 度\r\n");
                    // 获取并打印电机2的角度
                    int32_t angle2 = a4988_get_angle(2);
                    usartSendString("  电机2: ");
                    print_number(angle2);
                    usartSendString(" 度\r\n");
                    usartSendString("--------------------------\r\n");
                    break;
                }
                case 'd': {
                    // 'd' - Distance: 立即读取并显示一次校准后的距离
                    uint16_t raw_dist_d;
                    if (vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &raw_dist_d)) {
                        usartSendString("当前距离 (已校准): ");
                        if (raw_dist_d == VL53L0X_OUT_OF_RANGE) {
                            usartSendString("超出范围 (>2000mm)\r\n");
                        } else {
                            // 应用校准偏移量
                            uint16_t calibrated_dist = (raw_dist_d > distance_offset) ? (raw_dist_d - distance_offset) : 0;
                            print_number(calibrated_dist);
                            usartSendString(" mm\r\n");
                        }
                    } else {
                        usartSendString("距离读取失败\r\n");
                    }
                    break;
                }
                case 't': {
                    // 't' - Test: I2C连接测试
                    usartSendString("=== I2C连接测试 ===\r\n");

                    // 测试I2C引脚状态
                    usartSendString("1. 检查I2C引脚状态:\r\n");
                    uint8_t scl_state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_6);
                    uint8_t sda_state = GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_7);
                    usartSendString("   SCL(PB6): ");
                    usartSendString(scl_state ? "HIGH\r\n" : "LOW\r\n");
                    usartSendString("   SDA(PB7): ");
                    usartSendString(sda_state ? "HIGH\r\n" : "LOW\r\n");

                    // 测试XSHUT引脚
                    usartSendString("2. 检查XSHUT引脚状态:\r\n");
                    uint8_t xshut_state = GPIO_ReadOutputDataBit(GPIOB, GPIO_Pin_0);
                    usartSendString("   XSHUT(PB0): ");
                    usartSendString(xshut_state ? "HIGH\r\n" : "LOW\r\n");

                    // 重新初始化I2C
                    usartSendString("3. 重新初始化I2C...\r\n");
                    if (i2c_init()) {
                        usartSendString("   ✓ I2C初始化成功\r\n");
                    } else {
                        usartSendString("   ✗ I2C初始化失败\r\n");
                    }

                    // 尝试VL53L0X初始化
                    usartSendString("4. 尝试VL53L0X初始化...\r\n");
                    if (vl53l0x_init()) {
                        usartSendString("   ✓ VL53L0X初始化成功\r\n");
                    } else {
                        usartSendString("   ✗ VL53L0X初始化失败\r\n");
                    }

                    usartSendString("==================\r\n");
                    break;
                }
                case 'i': {
                    // 'i' - Info: 显示系统硬件信息、设备ID和当前的原始/校准距离
                    usartSendString("========== 系统信息 ==========\r\n");
                    usartSendString("主控: STM32F103\r\n");
                    usartSendString("I2C接口: 软件模拟 (SDA: PB7, SCL: PB6)\r\n");
                    usartSendString("传感器XSHUT引脚: PB0\r\n");
                    usartSendString("按键1 (PB1): 电机2测试, 按键2 (PB11): 自动运行\r\n");
                    usartSendString("串口波特率: 115200\r\n");
                    usartSendString("--------------------------------\r\n");
                    // 测试I2C通信
                    i2c_set_slave_address(0x29);
                    uint8_t device_id = 0;
                    if (i2c_read_addr8_data8(0xC0, &device_id)) {
                        usartSendString("VL53L0X 设备ID: 0x");
                        uint8_t high = (device_id >> 4) & 0x0F;
                        uint8_t low = device_id & 0x0F;
                        usartSendByte(high < 10 ? '0' + high : 'A' + high - 10);
                        usartSendByte(low < 10 ? '0' + low : 'A' + low - 10);
                        usartSendString(" (期望值: 0xEE)\r\n");
                    } else {
                        usartSendString("VL53L0X 设备ID: 读取失败\r\n");
                    }
                    // 当前距离
                    uint16_t current_dist;
                    if (vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &current_dist)) {
                        usartSendString("当前距离 (原始值): ");
                        if (current_dist == VL53L0X_OUT_OF_RANGE) {
                            usartSendString("超出范围\r\n");
                        } else {
                            print_number(current_dist);
                            usartSendString(" mm | 校准后: ");
                            uint16_t calibrated_dist_i = (current_dist > distance_offset) ? (current_dist - distance_offset) : 0;
                            print_number(calibrated_dist_i);
                            usartSendString(" mm\r\n");
                        }
                    } else {
                        usartSendString("当前距离: 读取失败\r\n");
                    }
                    usartSendString("============================\r\n");
                    break;
                }
                case 'k': { // 'k' - Kalibrate: 执行距离校准流程
                    usartSendString("校准: 请在100mm处放置物体\r\n");
                    usartSendString("按 'y' 开始，其他键取消\r\n");
                    // 1. 等待用户确认
                    uint8_t confirmChar = 0;
                    bool cancelled = false;
                    while(1) {
                        if (usartGetReceivedByte(&confirmChar)) {
                            if (confirmChar != 'y') {
                                usartSendString("已取消\r\n");
                                cancelled = true;
                            }
                            break;
                        }
                    }
                    if (cancelled) {
                        break; // 如果用户取消，则跳出校准流程
                    }
                    // 2. 进行多次测量并取平均值，以减少单次测量的误差
                    usartSendString("校准中... 读取10个样本\r\n");
                    uint16_t avg_reading;
                    if (vl53l0x_read_range_average(VL53L0X_IDX_FIRST, &avg_reading, 10)) {
                        if (avg_reading != VL53L0X_OUT_OF_RANGE) {
                            // 3. 计算偏移量 (测量值 - 真实值)
                            distance_offset = avg_reading - 100;
                            usartSendString("测量平均值: ");
                            print_number(avg_reading);
                            usartSendString(" mm\r\n");
                            usartSendString("新偏移量: ");
                            print_number(distance_offset);
                            usartSendString(" mm\r\n");
                            // 4. 将新的偏移量写入Flash，实现掉电保持
                            usartSendString("保存偏移量到Flash存储器... ");
                            if (storage_write_offset(distance_offset)) {
                                usartSendString("完成\r\n");
                            } else {
                                usartSendString("失败!\r\n");
                            }
                            usartSendString("校准完成!\r\n");
                        } else {
                            usartSendString("校准失败: 超出范围\r\n");
                        }
                    } else {
                        usartSendString("校准失败: 传感器读取错误\r\n");
                    }
                    break;
                }
                case 'c': {
                    // 'c' - Calibration Test: 连续进行3次单次测量，用于快速检查稳定性
                    usartSendString("开始校准测试...\r\n");
                    usartSendString("进行3次测量:\r\n");
                    uint32_t total_distance = 0;
                    uint8_t valid_count = 0;
                    for (int i = 0; i < 3; i++) {
                        uint16_t single_distance;
                        usartSendString("  ");
                        // 发送测量次数
                        char numStr[5];
                        int len = 0;
                        int temp = i + 1;
                        do {
                            numStr[len++] = '0' + (temp % 10);
                            temp /= 10;
                        } while (temp > 0);
                        for (int j = len - 1; j >= 0; j--) {
                            usartSendByte(numStr[j]);
                        }
                        usartSendString(": ");
                        if (vl53l0x_read_range_single(VL53L0X_IDX_FIRST, &single_distance)) {
                            if (single_distance == VL53L0X_OUT_OF_RANGE) {
                                usartSendString("超出范围\r\n");
                            } else {
                                // 发送距离数值
                                char distanceStr[10];
                                len = 0;
                                uint16_t temp_dist = single_distance;
                                do {
                                    distanceStr[len++] = '0' + (temp_dist % 10);
                                    temp_dist /= 10;
                                } while (temp_dist > 0);
                                for (int j = len - 1; j >= 0; j--) {
                                    usartSendByte(distanceStr[j]);
                                }
                                usartSendString(" mm\r\n");
                                total_distance += single_distance;
                                valid_count++;
                            }
                        } else {
                            usartSendString("失败\r\n");
                        }
                        Delay_ms(20); // 减少延时到20ms
                    }
                    // 显示统计结果
                    if (valid_count > 0) {
                        uint16_t avg_distance = (uint16_t)(total_distance / valid_count);
                        usartSendString("有效测量: ");
                        char countStr[5];
                        int len = 0;
                        int temp = valid_count;
                        do {
                            countStr[len++] = '0' + (temp % 10);
                            temp /= 10;
                        } while (temp > 0);
                        for (int i = len - 1; i >= 0; i--) {
                            usartSendByte(countStr[i]);
                        }
                        usartSendString("\r\n");
                        usartSendString("平均距离: ");
                        char avgStr[10];
                        len = 0;
                        uint16_t temp_avg = avg_distance;
                        do {
                            avgStr[len++] = '0' + (temp_avg % 10);
                            temp_avg /= 10;
                        } while (temp_avg > 0);
                        for (int i = len - 1; i >= 0; i--) {
                            usartSendByte(avgStr[i]);
                        }
                        usartSendString(" mm\r\n");
                    } else {
                        usartSendString("无有效测量\r\n");
                    }
                    usartSendString("校准测试完成\r\n");
                    break;
                }
                case 'u': { // 'u' - Upload: 手动设置回放位置数据
                    usartSendString("=== 手动设置回放位置数据 ===\r\n");
                    usartSendString("将覆盖现有记录数据\r\n");
                    usartSendString("输入格式: 第1组,电机1角度,电机2角度-第2组,电机1角度,电机2角度-...\r\n");
                    usartSendString("例如: 第1组,90,180-第2组,45,270-第3组,0,360\r\n");
                    usartSendString("输入 'q' 退出\r\n");
                    usartSendString("请输入所有数据: ");

                    // 清空当前记录数据
                    for (int i = 0; i < 20; i++) {
                        recorded_data[i].angle1 = 0;
                        recorded_data[i].angle2 = 0;
                    }

                    // 接收完整的输入字符串
                    char input_buffer[512] = {0}; // 增大缓冲区以容纳更多数据
                    int buffer_index = 0;
                    bool input_complete = false;
                    bool cancelled = false;

                    // 接收字符直到换行或缓冲区满
                    while (!input_complete && buffer_index < 511) {
                        uint8_t ch;
                        if (usartGetReceivedByte(&ch)) {
                            if (ch == '\r' || ch == '\n') {
                                input_complete = true;
                                usartSendString("\r\n");
                            } else if (ch == 'q' || ch == 'Q') {
                                usartSendString("已取消设置\r\n");
                                cancelled = true;
                                input_complete = true;
                            } else {
                                input_buffer[buffer_index++] = ch;
                                usartSendByte(ch); // 回显
                            }
                        }
                    }

                    if (cancelled) {
                        break;
                    }

                    // 解析输入的数据
                    int data_count = 0;
                    int pos = 0;

                    usartSendString("开始解析数据...\r\n");

                    while (pos < buffer_index && data_count < 20) {
                        // 查找数字或逗号
                        while (pos < buffer_index &&
                               !(input_buffer[pos] >= '0' && input_buffer[pos] <= '9') &&
                               input_buffer[pos] != '-' && input_buffer[pos] != ',') {
                            pos++;
                        }

                        if (pos >= buffer_index) break;

                        // 跳过到第一个逗号
                        while (pos < buffer_index && input_buffer[pos] != ',') {
                            pos++;
                        }

                        if (pos >= buffer_index) break;
                        pos++; // 跳过逗号

                        // 解析电机1角度
                        int32_t angle1_input = 0;
                        bool negative1 = false;

                        if (pos < buffer_index && input_buffer[pos] == '-') {
                            negative1 = true;
                            pos++;
                        }

                        while (pos < buffer_index && input_buffer[pos] >= '0' && input_buffer[pos] <= '9') {
                            angle1_input = angle1_input * 10 + (input_buffer[pos] - '0');
                            pos++;
                        }

                        if (negative1) angle1_input = -angle1_input;

                        // 跳过逗号
                        if (pos < buffer_index && input_buffer[pos] == ',') {
                            pos++;
                        } else {
                            usartSendString("格式错误: 缺少逗号分隔符\r\n");
                            break;
                        }

                        // 解析电机2角度
                        int32_t angle2_input = 0;
                        bool negative2 = false;

                        if (pos < buffer_index && input_buffer[pos] == '-') {
                            negative2 = true;
                            pos++;
                        }

                        while (pos < buffer_index && input_buffer[pos] >= '0' && input_buffer[pos] <= '9') {
                            angle2_input = angle2_input * 10 + (input_buffer[pos] - '0');
                            pos++;
                        }

                        if (negative2) angle2_input = -angle2_input;

                        // 保存数据
                        recorded_data[data_count].angle1 = angle1_input;
                        recorded_data[data_count].angle2 = angle2_input;

                        usartSendString("Group ");
                        print_number(data_count + 1);
                        usartSendString(": Motor1=");
                        print_number(angle1_input);
                        usartSendString("deg, Motor2=");
                        print_number(angle2_input);
                        usartSendString("deg\r\n");

                        data_count++;

                        // 跳过分隔符"-"
                        if (pos < buffer_index && input_buffer[pos] == '-') {
                            pos++;
                        }
                    }

                    autoMotor1Rounds = data_count; // 更新已存储点数

                    usartSendString("已设置 ");
                    print_number(data_count);
                    usartSendString(" 组回放数据\r\n");
                    usartSendString("可使用按键2或'r'命令开始回放\r\n");
                    break;
                }
                case 'p': { // 'p' - Precision: 高精度距离测量
                    usartSendString("=== 高精度距离测量 ===\r\n");
                    usartSendString("采样次数 (5-20): ");
                    
                    // 等待用户输入采样次数
                    uint8_t samples = 10; // 默认值
                    uint8_t input_char;
                    uint8_t input_num = 0;
                    while(1) {
                        if (usartGetReceivedByte(&input_char)) {
                            if (input_char >= '0' && input_char <= '9') {
                                input_num = input_num * 10 + (input_char - '0');
                                usartSendByte(input_char);
                            } else if (input_char == '\r' || input_char == '\n') {
                                if (input_num >= 5 && input_num <= 20) {
                                    samples = input_num;
                                }
                                usartSendString("\r\n");
                                break;
                            }
                        }
                    }
                    
                    usartSendString("开始高精度测量 (");
                    print_number(samples);
                    usartSendString(" 次采样)...\r\n");
                    
                    uint16_t high_precision_dist;
                    if (vl53l0x_read_range_high_precision(VL53L0X_IDX_FIRST, &high_precision_dist, samples)) {
                        if (high_precision_dist != VL53L0X_OUT_OF_RANGE) {
                            usartSendString("高精度距离: ");
                            print_number(high_precision_dist);
                            usartSendString(" mm (原始值)\r\n");
                            
                            // 应用校准偏移量
                            int16_t offset = storage_read_offset();
                            uint16_t calibrated = (high_precision_dist > offset) ? (high_precision_dist - offset) : 0;
                            usartSendString("校准后距离: ");
                            print_number(calibrated);
                            usartSendString(" mm\r\n");
                        } else {
                            usartSendString("测量失败: 超出范围\r\n");
                        }
                    } else {
                        usartSendString("高精度测量失败\r\n");
                    }
                    break;
                }
                case 'z': { // 'z' - aZaptive: 自适应精度测量
                    usartSendString("=== 自适应精度测量 ===\r\n");
                    usartSendString("目标精度 (1-5mm): ");
                    
                    // 等待用户输入目标精度
                    uint8_t target_accuracy = 2; // 默认2mm精度
                    uint8_t input_char;
                    while(1) {
                        if (usartGetReceivedByte(&input_char)) {
                            if (input_char >= '1' && input_char <= '5') {
                                target_accuracy = input_char - '0';
                                usartSendByte(input_char);
                                usartSendString("\r\n");
                                break;
                            } else if (input_char == '\r' || input_char == '\n') {
                                usartSendString("\r\n");
                                break;
                            }
                        }
                    }
                    
                    usartSendString("开始自适应测量 (目标精度: ");
                    print_number(target_accuracy);
                    usartSendString("mm)...\r\n");
                    
                    uint16_t adaptive_dist;
                    if (vl53l0x_read_range_adaptive(VL53L0X_IDX_FIRST, &adaptive_dist, target_accuracy)) {
                        if (adaptive_dist != VL53L0X_OUT_OF_RANGE) {
                            usartSendString("自适应距离: ");
                            print_number(adaptive_dist);
                            usartSendString(" mm (原始值)\r\n");
                            
                            // 应用校准偏移量
                            int16_t offset = storage_read_offset();
                            uint16_t calibrated = (adaptive_dist > offset) ? (adaptive_dist - offset) : 0;
                            usartSendString("校准后距离: ");
                            print_number(calibrated);
                            usartSendString(" mm\r\n");
                        } else {
                            usartSendString("测量失败: 超出范围\r\n");
                        }
                    } else {
                        usartSendString("自适应测量失败\r\n");
                    }
                    break;
                }
                case 'x': { // 'x' - continuous: 连续测量模式
                    usartSendString("=== 连续测量模式 ===\r\n");
                    usartSendString("启用滤波? (y/n): ");
                    
                    // 等待用户选择是否启用滤波
                    bool filter_enabled = false;
                    uint8_t input_char;
                    while(1) {
                        if (usartGetReceivedByte(&input_char)) {
                            if (input_char == 'y' || input_char == 'Y') {
                                filter_enabled = true;
                                usartSendString("y\r\n已启用低通滤波\r\n");
                                break;
                            } else if (input_char == 'n' || input_char == 'N') {
                                filter_enabled = false;
                                usartSendString("n\r\n已禁用滤波\r\n");
                                break;
                            } else if (input_char == '\r' || input_char == '\n') {
                                usartSendString("已禁用滤波\r\n");
                                break;
                            }
                        }
                    }
                    
                    usartSendString("连续测量中... (发送任意字符停止)\r\n");
                    
                    // 连续测量循环
                    uint16_t measurement_count = 0;
                    while(1) {
                        uint16_t continuous_dist;
                        if (vl53l0x_read_range_continuous(VL53L0X_IDX_FIRST, &continuous_dist, filter_enabled)) {
                            if (continuous_dist != VL53L0X_OUT_OF_RANGE) {
                                measurement_count++;
                                usartSendString("[");
                                print_number(measurement_count);
                                usartSendString("] ");
                                print_number(continuous_dist);
                                usartSendString("mm");
                                if (filter_enabled) {
                                    usartSendString(" (滤波)");
                                }
                                usartSendString("\r\n");
                            } else {
                                usartSendString("超出范围\r\n");
                            }
                        } else {
                            usartSendString("读取失败\r\n");
                        }
                        
                        // 检查是否有用户输入要停止
                        uint8_t stop_char;
                        if (usartGetReceivedByte(&stop_char)) {
                            usartSendString("连续测量已停止\r\n");
                            break;
                        }
                        
                        Delay_ms(200); // 200ms间隔
                    }
                    break;
                }
                case 'r': { // 'r' - Replay: 启动回放流程
                    usartSendString("启动回放流程\r\n");
                    autoRunState = AUTO_REPLAY_START;
                    break;
                }
                case 'h': { // 'h' - Help: 显示帮助菜单
                    usartSendString("=============== 帮助菜单 ===============\r\n");
                    usartSendString("--- 按键控制 ---\r\n");
                    usartSendString("  按键1 (PB1): 电机2测试\r\n");
                    usartSendString("  按键2 (PB11): 自动运行程序\r\n");
                    usartSendString("\r\n");
                    usartSendString("--- 电机测试 ---\r\n");
                    usartSendString("  1: 电机1测试 (软件脉冲法)\r\n");
                    usartSendString("  2: 电机2测试 (PWM驱动法)\r\n");
                    usartSendString("  0: 停止所有电机\r\n");
                    usartSendString("\r\n");
                    usartSendString("--- 传感器与系统 ---\r\n");
                    usartSendString("  g: 获取电机当前角度\r\n");
                    usartSendString("  d: 读取当前距离 (已校准)\r\n");
                    usartSendString("  v: 查看记录的角度\r\n");
                    usartSendString("  u: 手动设置回放位置数据\r\n");
                    usartSendString("  p: 高精度距离测量\r\n");
                    usartSendString("  z: 自适应精度测量\r\n");
                    usartSendString("  x: 连续测量模式\r\n");
                    usartSendString("  c: 快速校准测试 (3次)\r\n");
                    usartSendString("  k: 运行距离校准程序\r\n");
                    usartSendString("  i: 显示系统信息\r\n");
                    usartSendString("\r\n");
                    usartSendString("--- 其他 ---\r\n");
                    usartSendString("  r: 启动回放流程\r\n");
                    usartSendString("  t: 电机驱动测试 (低速启动验证)\r\n");
                    usartSendString("  h: 显示本帮助菜单\r\n");
                    usartSendString("======================================\r\n");
                    break;
                }
                default: // 未知指令
                    usartSendString("未知命令。发送 'h' 查看帮助\r\n");
                    break;
            }
        }
        counter++;        // 增加循环计数器
        Delay_ms(10);     // 延时10毫秒，防止CPU空转
    }
}
