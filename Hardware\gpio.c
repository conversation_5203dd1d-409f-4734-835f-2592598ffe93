#include "gpio.h"
#include "stm32f10x.h"
#include <stdint.h>

// Define GPIO pins for XSHUT control
// Using GPIOB pin 0 for XSHUT control
#define XSHUT_FIRST_PIN     GPIO_Pin_0
#define XSHUT_GPIO_PORT     GPIOB

void gpio_init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // Enable GPIOB clock
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);

    // Configure all XSHUT pins as output push-pull
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    
    // Configure first XSHUT pin
    GPIO_InitStructure.GPIO_Pin = XSHUT_FIRST_PIN;
    GPIO_Init(XSHUT_GPIO_PORT, &GPIO_InitStructure);
    GPIO_ResetBits(XSHUT_GPIO_PORT, XSHUT_FIRST_PIN);
}

void gpio_set_output(gpio_t gpio, bool enable)
{
    uint16_t pin = 0;
    
    if (gpio == GPIO_XSHUT_FIRST) {
        pin = XSHUT_FIRST_PIN;
    }
#ifdef VL53L0X_SECOND
    else if (gpio == GPIO_XSHUT_SECOND) {
        pin = GPIO_Pin_1; // GPIOB Pin 1 for second sensor
    }
#endif
#ifdef VL53L0X_THIRD
    else if (gpio == GPIO_XSHUT_THIRD) {
        pin = GPIO_Pin_2; // GPIOB Pin 2 for third sensor
    }
#endif

    if (enable) {
        GPIO_SetBits(XSHUT_GPIO_PORT, pin);
    } else {
        GPIO_ResetBits(XSHUT_GPIO_PORT, pin);
    }
}
