#include "Delay.h"

volatile DelayState_t delay_state = {0};
static volatile uint32_t system_tick_ms = 0;

/**
  * @brief  初始化延时
  */
void Delay_Init(void)
{
    // 配置SysTick为10us中断 (72MHz HCLK)
    if (SysTick_Config(SystemCoreClock / 100000)) { // 100kHz
        while (1); // 初始化失败
    }
    NVIC_SetPriority(SysTick_IRQn, 2); // 优先级调低
}

/**
  * @brief  微秒级延时（最小分辨率10us）
  */
void Delay_us(uint32_t us)
{
    // 转换为10us单位
    uint32_t units = (us + 5) / 10; // 四舍五入
    delay_state.counter = units;
    while (delay_state.counter > 0);
}

/**
  * @brief  毫秒级延时
  */
void Delay_ms(uint32_t ms)
{
    while (ms--) {
        Delay_us(1000);
    }
}

/**
  * @brief  秒级延时
  */
void Delay_s(uint32_t s)
{
    while (s--) {
        Delay_ms(1000);
    }
}

/**
  * @brief  This function handles SysTick Handler.
  * @param  None
  * @retval None
  * @note   This function is declared as a weak symbol in the startup file.
  *         Defining it here overrides the default empty handler.
  */
void SysTick_Handler(void)
{
    if (delay_state.counter > 0)
        delay_state.counter--;

    // 每100次SysTick中断（1ms）增加一次毫秒计数
    static uint8_t ms_counter = 0;
    if (++ms_counter >= 100) {
        ms_counter = 0;
        system_tick_ms++;
    }

    stepper1_software_tick(); // 每10us自动调用
}

/**
  * @brief  获取系统时钟计数（毫秒）
  * @retval 系统运行时间（毫秒）
  */
uint32_t Delay_GetTick(void)
{
    return system_tick_ms;
}
