#include "vl53l0x.h"
#include "i2c.h"
#include "gpio.h"
#include "stm32f10x.h"

#include "Delay.h"                  // Device header
#define REG_IDENTIFICATION_MODEL_ID (0xC0)
#define REG_VHV_CONFIG_PAD_SCL_SDA_EXTSUP_HV (0x89)
#define REG_MSRC_CONFIG_CONTROL (0x60)
#define REG_FINAL_RANGE_CONFIG_MIN_COUNT_RTN_LIMIT (0x44)
#define REG_SYSTEM_SEQUENCE_CONFIG (0x01)
#define REG_DYNAMIC_SPAD_REF_EN_START_OFFSET (0x4F)
#define REG_DYNAMIC_SPAD_NUM_REQUESTED_REF_SPAD (0x4E)
#define REG_GLOBAL_CONFIG_REF_EN_START_SELECT (0xB6)
#define REG_SYSTEM_INTERRUPT_CONFIG_GPIO (0x0A)
#define REG_GPIO_HV_MUX_ACTIVE_HIGH (0x84)
#define REG_SYSTEM_INTERRUPT_CLEAR (0x0B)
#define REG_RESULT_INTERRUPT_STATUS (0x13)
#define REG_SYSRANGE_START (0x00)
#define REG_GLOBAL_CONFIG_SPAD_ENABLES_REF_0 (0xB0)
#define REG_RESULT_RANGE_STATUS (0x14)
#define REG_SLAVE_DEVICE_ADDRESS (0x8A)

#define RANGE_SEQUENCE_STEP_TCC (0x10) /* 目标中心检查 */
#define RANGE_SEQUENCE_STEP_MSRC (0x04) /* 最小信号率检查 */
#define RANGE_SEQUENCE_STEP_DSS (0x28) /* 动态SPAD选择 */
#define RANGE_SEQUENCE_STEP_PRE_RANGE (0x40)
#define RANGE_SEQUENCE_STEP_FINAL_RANGE (0x80)

#define VL53L0X_EXPECTED_DEVICE_ID (0xEE)
#define VL53L0X_DEFAULT_ADDRESS (0x29)

/* SPAD有两种类型：孔径型和非孔径型。我的理解是
 * 孔径型让光线进入较少（它们有较小的开口），类似于
 * 数码相机上的光圈调节。只有1/4的SPAD是非孔径型。 */
#define SPAD_TYPE_APERTURE (0x01)
/* 整个SPAD阵列是16x16，但我们一次只能激活一个包含44个SPAD的象限。
 * 在ST的API代码中，他们（出于某种原因）选择了0xB4（180）作为起始点
 * （位于中间，跨越非孔径（第3）象限和孔径（第4）象限）。 */
#define SPAD_START_SELECT (0xB4)
/* 整个SPAD映射是16x16，但我们一次应该只激活44个SPAD的区域。 */
#define SPAD_MAX_COUNT (44)
/* 44个SPAD用6个字节表示，每个位代表一个SPAD。
 * 6x8 = 48，所以最后4位未使用。 */
#define SPAD_MAP_ROW_COUNT (6)
#define SPAD_ROW_SIZE (8)
/* 由于我们从0xB4（180）开始，有四个象限（三个孔径，一个非孔径），
 * 每个象限包含256/4 = 64个SPAD，第三个象限是非孔径型，
 * 到孔径象限的偏移量是(256 - 64 - 180) = 12 */
#define SPAD_APERTURE_START_INDEX (12)

typedef struct vl53l0x_info
{
    uint8_t addr;
    gpio_t xshut_gpio;
} vl53l0x_info_t;

typedef enum
{
    CALIBRATION_TYPE_VHV,
    CALIBRATION_TYPE_PHASE
} calibration_type_t;

static const vl53l0x_info_t vl53l0x_infos[] =
{
    [VL53L0X_IDX_FIRST] = { .addr = 0x30, .xshut_gpio = GPIO_XSHUT_FIRST },
#ifdef VL53L0X_SECOND
    [VL53L0X_IDX_SECOND] = { .addr = 0x31, .xshut_gpio = GPIO_XSHUT_SECOND },
#endif
#ifdef VL53L0X_THIRD
    [VL53L0X_IDX_THIRD] = { .addr = 0x32, .xshut_gpio = GPIO_XSHUT_THIRD },
#endif
};

static uint8_t stop_variable = 0;

/**
 * 我们可以读取型号ID来确认设备已启动。
 * （与vl6180x不同，这里没有fresh_out_of_reset标志）
 */
static bool device_is_booted()
{
    uint8_t device_id = 0;
    if (!i2c_read_addr8_data8(REG_IDENTIFICATION_MODEL_ID, &device_id)) {
        return false;
    }
    return device_id == VL53L0X_EXPECTED_DEVICE_ID;
}

/**
 * 一次性设备初始化
 */
static bool data_init()
{
    bool success = false;

    /* 设置2.8V模式 */
    uint8_t vhv_config_scl_sda = 0;
    if (!i2c_read_addr8_data8(REG_VHV_CONFIG_PAD_SCL_SDA_EXTSUP_HV, &vhv_config_scl_sda)) {
        return false;
    }
    vhv_config_scl_sda |= 0x01;
    if (!i2c_write_addr8_data8(REG_VHV_CONFIG_PAD_SCL_SDA_EXTSUP_HV, vhv_config_scl_sda)) {
        return false;
    }

    /* 设置I2C标准模式 */
    success = i2c_write_addr8_data8(0x88, 0x00);

    success &= i2c_write_addr8_data8(0x80, 0x01);
    success &= i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x00, 0x00);
    /* 可能不需要为每个传感器获取停止变量 */
    success &= i2c_read_addr8_data8(0x91, &stop_variable);
    success &= i2c_write_addr8_data8(0x00, 0x01);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(0x80, 0x00);

    return success;
}

/**
 * 等待strobe值被设置。这用于从NVM（非易失性存储器）读取值时。
 */
static bool read_strobe()
{
    bool success = false;
    uint8_t strobe = 0;
    if (!i2c_write_addr8_data8(0x83, 0x00)) {
        return false;
    }
    do {
        success = i2c_read_addr8_data8(0x83, &strobe);
    } while (success && (strobe == 0));
    if (!success) {
        return false;
    }
    if (!i2c_write_addr8_data8(0x83, 0x01)) {
        return false;
    }
    return true;
}

/**
 * 获取ST在生产线上存储在NVM中的SPAD数量、SPAD类型和"良好"SPAD映射。
 * 
 * 根据数据手册，ST在出厂时运行校准（无盖玻璃）并将"良好"的SPAD映射保存到NVM（非易失性存储器）。
 * SPAD阵列有两种类型的SPAD：孔径型和非孔径型。默认情况下，所有良好的SPAD都被启用，
 * 但我们只应该启用其中的一个子集以获得优化的信号率。我们还应该只启用孔径型或非孔径型SPAD。
 * 要启用的SPAD数量和类型也在ST工厂的校准步骤中保存，可以从NVM中检索。
 */
static bool get_spad_info_from_nvm(uint8_t *spad_count, uint8_t *spad_type, uint8_t good_spad_map[6])
{
    bool success = false;
    uint8_t tmp_data8 = 0;
    uint32_t tmp_data32 = 0;

    /* 设置从NVM读取 */
    success  = i2c_write_addr8_data8(0x80, 0x01);
    success &= i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x00, 0x00);
    success &= i2c_write_addr8_data8(0xFF, 0x06);
    success &= i2c_read_addr8_data8(0x83, &tmp_data8);
    success &= i2c_write_addr8_data8(0x83, tmp_data8 | 0x04);
    success &= i2c_write_addr8_data8(0xFF, 0x07);
    success &= i2c_write_addr8_data8(0x81, 0x01);
    success &= i2c_write_addr8_data8(0x80, 0x01);
    if (!success) {
      return false;
    }

    /* 获取SPAD数量和类型 */
    success &= i2c_write_addr8_data8(0x94, 0x6b);
    if (!success) {
        return false;
    }
    if (!read_strobe()) {
        return false;
    }
    success &= i2c_read_addr8_data32(0x90, &tmp_data32);
    if (!success) {
        return false;
    }
    *spad_count = (tmp_data32 >> 8) & 0x7f;
    *spad_type = (tmp_data32 >> 15) & 0x01;

    /* 由于良好的SPAD映射已经存储在REG_GLOBAL_CONFIG_SPAD_ENABLES_REF_0中，
     * 我们可以直接读取该寄存器，而不是执行下面的操作 */
#if 0
    /* 获取SPAD映射的第一部分 */
    if (!i2c_write_addr8_data8(0x94, 0x24)) {
        return false;
    }
    if (!read_strobe()) {
        return false;
    }
    if (!i2c_read_addr8_data32(0x90, &tmp_data32)) {
      return false;
    }
    good_spad_map[0] = (uint8_t)((tmp_data32 >> 24) & 0xFF);
    good_spad_map[1] = (uint8_t)((tmp_data32 >> 16) & 0xFF);
    good_spad_map[2] = (uint8_t)((tmp_data32 >> 8) & 0xFF);
    good_spad_map[3] = (uint8_t)(tmp_data32 & 0xFF);

    /* 获取SPAD映射的第二部分 */
    if (!i2c_write_addr8_data8(0x94, 0x25)) {
        return false;
    }
    if (!read_strobe()) {
        return false;
    }
    if (!i2c_read_addr8_data32(0x90, &tmp_data32)) {
        return false;
    }
    good_spad_map[4] = (uint8_t)((tmp_data32 >> 24) & 0xFF);
    good_spad_map[5] = (uint8_t)((tmp_data32 >> 16) & 0xFF);
#endif

    /* 从NVM读取后恢复 */
    success &=i2c_write_addr8_data8(0x81, 0x00);
    success &=i2c_write_addr8_data8(0xFF, 0x06);
    success &=i2c_read_addr8_data8(0x83, &tmp_data8);
    success &=i2c_write_addr8_data8(0x83, tmp_data8 & 0xfb);
    success &=i2c_write_addr8_data8(0xFF, 0x01);
    success &=i2c_write_addr8_data8(0x00, 0x01);
    success &=i2c_write_addr8_data8(0xFF, 0x00);
    success &=i2c_write_addr8_data8(0x80, 0x00);

    /* 当我们还没有配置SPAD映射时，SPAD映射寄存器实际上
     * 包含良好的SPAD映射，所以我们可以直接从该寄存器获取它，
     * 而不是从NVM读取。 */
    if (!i2c_read_addr8_bytes(REG_GLOBAL_CONFIG_SPAD_ENABLES_REF_0, good_spad_map, 6)) {
        return false;
    }
    return success;
}

/**
 * 根据ST在生产过程中保存到NVM的值设置SPAD。假设
 * 类似的条件（例如，无盖玻璃），这应该给出合理的读数，我们可以
 * 避免运行参考SPAD管理（繁琐的代码）。
 */
static bool set_spads_from_nvm()
{
    uint8_t spad_map[SPAD_MAP_ROW_COUNT] = { 0 };
    uint8_t good_spad_map[SPAD_MAP_ROW_COUNT] = { 0 };
    uint8_t spads_enabled_count = 0;
    uint8_t spads_to_enable_count = 0;
    uint8_t spad_type = 0;
    volatile uint32_t total_val = 0;

    if (!get_spad_info_from_nvm(&spads_to_enable_count, &spad_type, good_spad_map)) {
        return false;
    }

    for (int i = 0; i < 6; i++) {
        total_val += good_spad_map[i];
    }

    bool success = i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(REG_DYNAMIC_SPAD_REF_EN_START_OFFSET, 0x00);
    success &= i2c_write_addr8_data8(REG_DYNAMIC_SPAD_NUM_REQUESTED_REF_SPAD, 0x2C);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(REG_GLOBAL_CONFIG_REF_EN_START_SELECT, SPAD_START_SELECT);
    if (!success) {
        return false;
    }

    uint8_t offset = (spad_type == SPAD_TYPE_APERTURE) ? SPAD_APERTURE_START_INDEX : 0;

    /* 通过从良好SPAD映射中选择一个子集来创建新的SPAD数组。
     * 该子集应该只包含从NVM读取建议的类型和数量
     * （spads_to_enable_count和spad_type）。 */
    for (int row = 0; row < SPAD_MAP_ROW_COUNT; row++) {
        for (int column = 0; column < SPAD_ROW_SIZE; column++) {
            int index = (row * SPAD_ROW_SIZE) + column;
            if (index >= SPAD_MAX_COUNT) {
                return false;
            }
            if (spads_enabled_count == spads_to_enable_count) {
                /* 已完成 */
                break;
            }
            if (index < offset) {
                continue;
            }
            if ((good_spad_map[row] >> column) & 0x1) {
                spad_map[row] |= (1 << column);
                spads_enabled_count++;
            }
        }
        if (spads_enabled_count == spads_to_enable_count) {
            /* 避免在已完成时不必要的循环 */
            break;
        }
    }

    if (spads_enabled_count != spads_to_enable_count) {
        return false;
    }

    /* 写入新的SPAD配置 */
    if (!i2c_write_addr8_bytes(REG_GLOBAL_CONFIG_SPAD_ENABLES_REF_0, spad_map, SPAD_MAP_ROW_COUNT)) {
        return false;
    }

    return true;
}

/**
 * 加载调谐设置（与ST API代码提供的默认调谐设置相同）
 */
static bool load_default_tuning_settings()
{
    bool success = i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x00, 0x00);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(0x09, 0x00);
    success &= i2c_write_addr8_data8(0x10, 0x00);
    success &= i2c_write_addr8_data8(0x11, 0x00);
    success &= i2c_write_addr8_data8(0x24, 0x01);
    success &= i2c_write_addr8_data8(0x25, 0xFF);
    success &= i2c_write_addr8_data8(0x75, 0x00);
    success &= i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x4E, 0x2C);
    success &= i2c_write_addr8_data8(0x48, 0x00);
    success &= i2c_write_addr8_data8(0x30, 0x20);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(0x30, 0x09);
    success &= i2c_write_addr8_data8(0x54, 0x00);
    success &= i2c_write_addr8_data8(0x31, 0x04);
    success &= i2c_write_addr8_data8(0x32, 0x03);
    success &= i2c_write_addr8_data8(0x40, 0x83);
    success &= i2c_write_addr8_data8(0x46, 0x25);
    success &= i2c_write_addr8_data8(0x60, 0x00);
    success &= i2c_write_addr8_data8(0x27, 0x00);
    success &= i2c_write_addr8_data8(0x50, 0x06);
    success &= i2c_write_addr8_data8(0x51, 0x00);
    success &= i2c_write_addr8_data8(0x52, 0x96);
    success &= i2c_write_addr8_data8(0x56, 0x08);
    success &= i2c_write_addr8_data8(0x57, 0x30);
    success &= i2c_write_addr8_data8(0x61, 0x00);
    success &= i2c_write_addr8_data8(0x62, 0x00);
    success &= i2c_write_addr8_data8(0x64, 0x00);
    success &= i2c_write_addr8_data8(0x65, 0x00);
    success &= i2c_write_addr8_data8(0x66, 0xA0);
    success &= i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x22, 0x32);
    success &= i2c_write_addr8_data8(0x47, 0x14);
    success &= i2c_write_addr8_data8(0x49, 0xFF);
    success &= i2c_write_addr8_data8(0x4A, 0x00);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(0x7A, 0x0A);
    success &= i2c_write_addr8_data8(0x7B, 0x00);
    success &= i2c_write_addr8_data8(0x78, 0x21);
    success &= i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x23, 0x34);
    success &= i2c_write_addr8_data8(0x42, 0x00);
    success &= i2c_write_addr8_data8(0x44, 0xFF);
    success &= i2c_write_addr8_data8(0x45, 0x26);
    success &= i2c_write_addr8_data8(0x46, 0x05);
    success &= i2c_write_addr8_data8(0x40, 0x40);
    success &= i2c_write_addr8_data8(0x0E, 0x06);
    success &= i2c_write_addr8_data8(0x20, 0x1A);
    success &= i2c_write_addr8_data8(0x43, 0x40);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(0x34, 0x03);
    success &= i2c_write_addr8_data8(0x35, 0x44);
    success &= i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x31, 0x04);
    success &= i2c_write_addr8_data8(0x4B, 0x09);
    success &= i2c_write_addr8_data8(0x4C, 0x05);
    success &= i2c_write_addr8_data8(0x4D, 0x04);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(0x44, 0x00);
    success &= i2c_write_addr8_data8(0x45, 0x20);
    success &= i2c_write_addr8_data8(0x47, 0x08);
    success &= i2c_write_addr8_data8(0x48, 0x28);
    success &= i2c_write_addr8_data8(0x67, 0x00);
    success &= i2c_write_addr8_data8(0x70, 0x04);
    success &= i2c_write_addr8_data8(0x71, 0x01);
    success &= i2c_write_addr8_data8(0x72, 0xFE);
    success &= i2c_write_addr8_data8(0x76, 0x00);
    success &= i2c_write_addr8_data8(0x77, 0x00);
    success &= i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x0D, 0x01);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(0x80, 0x01);
    success &= i2c_write_addr8_data8(0x01, 0xF8);
    success &= i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x8E, 0x01);
    success &= i2c_write_addr8_data8(0x00, 0x01);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(0x80, 0x00);
    return success;
}

static bool configure_interrupt()
{
    /* 新样本就绪时中断 */
    if (!i2c_write_addr8_data8(REG_SYSTEM_INTERRUPT_CONFIG_GPIO, 0x04)) {
        return false;
    }

    /* 配置为低电平有效，因为大多数开发板上的引脚都是上拉的 */
    uint8_t gpio_hv_mux_active_high = 0;
    if (!i2c_read_addr8_data8(REG_GPIO_HV_MUX_ACTIVE_HIGH, &gpio_hv_mux_active_high)) {
        return false;
    }
    gpio_hv_mux_active_high &= ~0x10;
    if (!i2c_write_addr8_data8(REG_GPIO_HV_MUX_ACTIVE_HIGH, gpio_hv_mux_active_high)) {
        return false;
    }

    if (!i2c_write_addr8_data8(REG_SYSTEM_INTERRUPT_CLEAR, 0x01)) {
        return false;
    }
    return true;
}

/**
 * 启用（或禁用）序列中的特定步骤
 */
static bool set_sequence_steps_enabled(uint8_t sequence_step)
{
    return i2c_write_addr8_data8(REG_SYSTEM_SEQUENCE_CONFIG, sequence_step);
}

/**
 * 基本设备初始化
 */
static bool static_init()
{
    if (!set_spads_from_nvm()) {
        return false;
    }

    if (!load_default_tuning_settings()) {
        return false;
    }

    if (!configure_interrupt()) {
        return false;
    }

    if (!set_sequence_steps_enabled(RANGE_SEQUENCE_STEP_DSS +
                                    RANGE_SEQUENCE_STEP_PRE_RANGE +
                                    RANGE_SEQUENCE_STEP_FINAL_RANGE)) {
        return false;
    }

    return true;
}

static bool perform_single_ref_calibration(calibration_type_t calib_type)
{
    uint8_t sysrange_start = 0;
    uint8_t sequence_config = 0;
    switch (calib_type)
    {
    case CALIBRATION_TYPE_VHV:
        sequence_config = 0x01;
        sysrange_start = 0x01 | 0x40;
        break;
    case CALIBRATION_TYPE_PHASE:
        sequence_config = 0x02;
        sysrange_start = 0x01 | 0x00;
        break;
    }
    if (!i2c_write_addr8_data8(REG_SYSTEM_SEQUENCE_CONFIG, sequence_config)) {
        return false;
    }
    if (!i2c_write_addr8_data8(REG_SYSRANGE_START, sysrange_start)) {
        return false;
    }
    /* 等待中断 */
    uint8_t interrupt_status = 0;
    bool success = false;
    do {
        success = i2c_read_addr8_data8(REG_RESULT_INTERRUPT_STATUS, &interrupt_status);
    } while (success && ((interrupt_status & 0x07) == 0));
    if (!success) {
        return false;
    }
    if (!i2c_write_addr8_data8(REG_SYSTEM_INTERRUPT_CLEAR, 0x01)) {
        return false;
    }

    if (!i2c_write_addr8_data8(REG_SYSRANGE_START, 0x00)) {
        return false;
    }
    return true;
}

/**
 * 根据数据手册，如果温度变化超过8度，需要重新运行温度校准。
 */
static bool perform_ref_calibration()
{
    if (!perform_single_ref_calibration(CALIBRATION_TYPE_VHV)) {
        return false;
    }
    if (!perform_single_ref_calibration(CALIBRATION_TYPE_PHASE)) {
        return false;
    }
    /* 恢复启用的序列步骤 */
    if (!set_sequence_steps_enabled(RANGE_SEQUENCE_STEP_DSS +
                                    RANGE_SEQUENCE_STEP_PRE_RANGE +
                                    RANGE_SEQUENCE_STEP_FINAL_RANGE)) {
        return false;
    }
    return true;
}

static bool configure_address(uint8_t addr)
{
    /* 7位地址 */
    return i2c_write_addr8_data8(REG_SLAVE_DEVICE_ADDRESS, addr & 0x7F);
}

/**
 * 通过翻转XSHUT引脚将传感器置于硬件待机状态。
 * @param idx 传感器索引
 * @param enable true: 激活传感器 (XSHUT高电平), false: 待机状态 (XSHUT低电平)
 */
static void set_hardware_standby(vl53l0x_idx_t idx, bool enable)
{
    gpio_set_output(vl53l0x_infos[idx].xshut_gpio, enable);
}

/**
 * 配置用于XSHUT引脚的GPIO。
 * 默认输出低电平意味着在此函数调用后传感器将处于硬件待机状态。
 *
 * 注意：引脚硬编码为P1.0、P1.1和P1.2。
 **/
static void configure_gpio()
{
    gpio_init();
    gpio_set_output(GPIO_XSHUT_FIRST, false);
    gpio_set_output(GPIO_XSHUT_SECOND, false);
    gpio_set_output(GPIO_XSHUT_THIRD, false);
}

/* 设置单个VL53L0X传感器的地址。
 * 此函数假设所有未配置的VL53L0X仍处于硬件待机状态。 */
static bool init_address(vl53l0x_idx_t idx)
{
    set_hardware_standby(idx, true);  // 唤醒传感器（设置XSHUT为高电平）
    i2c_set_slave_address(VL53L0X_DEFAULT_ADDRESS);

    /* 数据手册没有说明离开硬件待机状态需要等待多长时间，
     * 但使用与vl6180x相同的延迟似乎可以正常工作。 */
    Delay_ms(1); // 1ms延迟替代__delay_cycles(400)

    if (!device_is_booted()) {
        return false;
    }

    if (!configure_address(vl53l0x_infos[idx].addr)) {
        return false;
    }
    return true;
}

/**
 * 通过将传感器置于硬件待机状态然后
 * 按照AN4846中的描述一个一个唤醒它们来初始化传感器。
 */
static bool init_addresses()
{
    /* 将所有传感器置于硬件待机状态 */
    configure_gpio();

    /* 一个一个唤醒每个传感器并为每个传感器设置唯一地址 */
    if (!init_address(VL53L0X_IDX_FIRST)) {
        return false;
    }
#ifdef VL53L0X_SECOND
    if (!init_address(VL53L0X_IDX_SECOND)) {
        return false;
    }
#endif
#ifdef VL53L0X_THIRD
    if (!init_address(VL53L0X_IDX_THIRD)) {
        return false;
    }
#endif

    return true;
}

static bool init_config(vl53l0x_idx_t idx)
{
    i2c_set_slave_address(vl53l0x_infos[idx].addr);
    if (!data_init()) {
        return false;
    }
    if (!static_init()) {
        return false;
    }
    if (!perform_ref_calibration()) {
        return false;
    }
    return true;
}

bool vl53l0x_init()
{
    if (!init_addresses()) {
        return false;
    }
    if (!init_config(VL53L0X_IDX_FIRST)) {
        return false;
    }
#ifdef VL53L0X_SECOND
    if (!init_config(VL53L0X_IDX_SECOND)) {
        return false;
    }
#endif
#ifdef VL53L0X_THIRD
    if (!init_config(VL53L0X_IDX_THIRD)) {
        return false;
    }
#endif
    return true;
}

bool vl53l0x_read_range_single(vl53l0x_idx_t idx, uint16_t *range)
{
    i2c_set_slave_address(vl53l0x_infos[idx].addr);
    bool success = i2c_write_addr8_data8(0x80, 0x01);
    success &= i2c_write_addr8_data8(0xFF, 0x01);
    success &= i2c_write_addr8_data8(0x00, 0x00);
    success &= i2c_write_addr8_data8(0x91, stop_variable);
    success &= i2c_write_addr8_data8(0x00, 0x01);
    success &= i2c_write_addr8_data8(0xFF, 0x00);
    success &= i2c_write_addr8_data8(0x80, 0x00);
    if (!success) {
        return false;
    }

    if (!i2c_write_addr8_data8(REG_SYSRANGE_START, 0x01)) {
        return false;
    }

    uint8_t sysrange_start = 0;
    do {
        success = i2c_read_addr8_data8(REG_SYSRANGE_START, &sysrange_start);
    } while (success && (sysrange_start & 0x01));
    if (!success) {
        return false;
    }

    uint8_t interrupt_status = 0;
    do {
        success = i2c_read_addr8_data8(REG_RESULT_INTERRUPT_STATUS, &interrupt_status);
    } while (success && ((interrupt_status & 0x07) == 0));
    if (!success) {
        return false;
    }

    if (!i2c_read_addr8_data16(REG_RESULT_RANGE_STATUS + 10, range)) {
        return false;
    }

    if (!i2c_write_addr8_data8(REG_SYSTEM_INTERRUPT_CLEAR, 0x01)) {
        return false;
    }

    /* 当障碍物超出范围时可能返回8190或8191 */
    if (*range == 8190 || *range == 8191) {
        *range = VL53L0X_OUT_OF_RANGE;
    }

    return true;
}

/**
 * 设置测量模式以提高精度
 */
bool vl53l0x_set_mode(vl53l0x_idx_t idx, vl53l0x_mode_t mode)
{
    i2c_set_slave_address(vl53l0x_infos[idx].addr);
    
    switch (mode) {
        case VL53L0X_MODE_HIGH_ACCURACY:
            // 高精度模式：增加测量时间，提高精度
            // 设置更长的测量时间
            if (!i2c_write_addr8_data8(0xFF, 0x01)) return false;
            if (!i2c_write_addr8_data8(0x00, 0x00)) return false;
            if (!i2c_write_addr8_data8(0xFF, 0x00)) return false;
            if (!i2c_write_addr8_data8(0x80, 0x01)) return false;
            if (!i2c_write_addr8_data8(0xFF, 0x01)) return false;
            if (!i2c_write_addr8_data8(0x00, 0x00)) return false;
            if (!i2c_write_addr8_data8(0xFF, 0x00)) return false;
            if (!i2c_write_addr8_data8(0x80, 0x00)) return false;
            
            // 设置更长的预范围时间
            if (!i2c_write_addr8_data8(0x51, 0x0C)) return false; // 预范围时间
            if (!i2c_write_addr8_data8(0x52, 0x09)) return false; // 最终范围时间
            if (!i2c_write_addr8_data8(0x56, 0x08)) return false; // 测量时间预算
            if (!i2c_write_addr8_data8(0x57, 0x30)) return false; // 测量时间预算
            break;
            
        case VL53L0X_MODE_HIGH_SPEED:
            // 高速模式：减少测量时间，提高速度
            if (!i2c_write_addr8_data8(0xFF, 0x01)) return false;
            if (!i2c_write_addr8_data8(0x00, 0x00)) return false;
            if (!i2c_write_addr8_data8(0xFF, 0x00)) return false;
            if (!i2c_write_addr8_data8(0x80, 0x01)) return false;
            if (!i2c_write_addr8_data8(0xFF, 0x01)) return false;
            if (!i2c_write_addr8_data8(0x00, 0x00)) return false;
            if (!i2c_write_addr8_data8(0xFF, 0x00)) return false;
            if (!i2c_write_addr8_data8(0x80, 0x00)) return false;
            
            // 设置更短的测量时间
            if (!i2c_write_addr8_data8(0x51, 0x04)) return false; // 预范围时间
            if (!i2c_write_addr8_data8(0x52, 0x03)) return false; // 最终范围时间
            if (!i2c_write_addr8_data8(0x56, 0x08)) return false; // 测量时间预算
            if (!i2c_write_addr8_data8(0x57, 0x10)) return false; // 测量时间预算
            break;
            
        case VL53L0X_MODE_DEFAULT:
        default:
            // 使用默认设置
            break;
    }
    
    return true;
}

/**
 * 进行多次测量并返回平均值以提高精度
 */
bool vl53l0x_read_range_average(vl53l0x_idx_t idx, uint16_t *range, uint8_t samples)
{
    if (samples < 1 || samples > 10) {
        samples = 5; // 默认5次测量
    }
    
    uint32_t sum = 0;
    uint8_t valid_samples = 0;
    
    for (uint8_t i = 0; i < samples; i++) {
        uint16_t temp_range;
        if (vl53l0x_read_range_single(idx, &temp_range)) {
            if (temp_range != VL53L0X_OUT_OF_RANGE) {
                sum += temp_range;
                valid_samples++;
            }
        }
        // 短暂延时避免连续测量
        Delay_ms(10);
    }
    
    if (valid_samples > 0) {
        *range = (uint16_t)(sum / valid_samples);
        return true;
    } else {
        *range = VL53L0X_OUT_OF_RANGE;
        return false;
    }
}

/**
 * 高精度测量，带异常值过滤
 * 算法：收集多个样本，去除异常值，对剩余值求平均
 */
bool vl53l0x_read_range_high_precision(vl53l0x_idx_t idx, uint16_t *range, uint8_t samples)
{
    if (samples < 5 || samples > 20) {
        samples = 10; // 默认10次测量
    }
    
    uint16_t readings[20];
    uint8_t valid_count = 0;
    
    // 收集原始数据
    for (uint8_t i = 0; i < samples; i++) {
        uint16_t temp_range;
        if (vl53l0x_read_range_single(idx, &temp_range)) {
            if (temp_range != VL53L0X_OUT_OF_RANGE) {
                readings[valid_count] = temp_range;
                valid_count++;
            }
        }
        Delay_ms(8); // 稍短的延时提高测量速度
    }
    
    if (valid_count < 3) {
        *range = VL53L0X_OUT_OF_RANGE;
        return false;
    }
    
    // 简单冒泡排序
    for (uint8_t i = 0; i < valid_count - 1; i++) {
        for (uint8_t j = 0; j < valid_count - i - 1; j++) {
            if (readings[j] > readings[j + 1]) {
                uint16_t temp = readings[j];
                readings[j] = readings[j + 1];
                readings[j + 1] = temp;
            }
        }
    }
    
    // 去除最大最小值（异常值过滤）
    uint8_t start_idx = valid_count / 6;     // 去除最小的1/6
    uint8_t end_idx = valid_count - start_idx; // 去除最大的1/6
    
    if (end_idx <= start_idx) {
        start_idx = 0;
        end_idx = valid_count;
    }
    
    // 计算过滤后的平均值
    uint32_t sum = 0;
    uint8_t filtered_count = 0;
    for (uint8_t i = start_idx; i < end_idx; i++) {
        sum += readings[i];
        filtered_count++;
    }
    
    *range = (uint16_t)(sum / filtered_count);
    return true;
}

/**
 * 自适应精度测量，基于测量稳定性
 * 算法：动态调整采样数量直到达到目标精度
 */
bool vl53l0x_read_range_adaptive(vl53l0x_idx_t idx, uint16_t *range, uint8_t target_accuracy)
{
    if (target_accuracy < 1) target_accuracy = 1;
    if (target_accuracy > 5) target_accuracy = 5;
    
    const uint8_t max_samples = 15;
    const uint8_t min_samples = 3;
    
    uint16_t readings[15];
    uint8_t sample_count = 0;
    
    // 逐步增加采样数量直到满足精度要求
    for (uint8_t total_samples = min_samples; total_samples <= max_samples; total_samples += 2) {
        // 如果需要更多样本，继续采集
        while (sample_count < total_samples) {
            uint16_t temp_range;
            if (vl53l0x_read_range_single(idx, &temp_range)) {
                if (temp_range != VL53L0X_OUT_OF_RANGE) {
                    readings[sample_count] = temp_range;
                    sample_count++;
                }
            }
            Delay_ms(6);
        }
        
        if (sample_count < min_samples) continue;
        
        // 计算当前平均值和标准差
        uint32_t sum = 0;
        for (uint8_t i = 0; i < sample_count; i++) {
            sum += readings[i];
        }
        uint16_t mean = (uint16_t)(sum / sample_count);
        
        // 简化的标准差计算
        uint32_t variance_sum = 0;
        for (uint8_t i = 0; i < sample_count; i++) {
            int32_t diff = (int32_t)readings[i] - (int32_t)mean;
            variance_sum += (uint32_t)(diff * diff);
        }
        uint16_t std_dev = (uint16_t)(variance_sum / sample_count);
        
        // 检查是否达到目标精度（标准差小于目标精度）
        if (std_dev <= (target_accuracy * target_accuracy)) {
            *range = mean;
            return true;
        }
    }
    
    // 如果无法达到目标精度，返回最后的平均值
    if (sample_count > 0) {
        uint32_t sum = 0;
        for (uint8_t i = 0; i < sample_count; i++) {
            sum += readings[i];
        }
        *range = (uint16_t)(sum / sample_count);
        return true;
    }
    
    *range = VL53L0X_OUT_OF_RANGE;
    return false;
}

/**
 * 连续测量模式，带低通滤波
 * 算法：维护历史数据，应用指数移动平均滤波
 */
bool vl53l0x_read_range_continuous(vl53l0x_idx_t idx, uint16_t *range, bool filter_enabled)
{
    static uint16_t filtered_value = 0;
    static bool first_measurement = true;
    
    uint16_t current_reading;
    if (!vl53l0x_read_range_single(idx, &current_reading)) {
        return false;
    }
    
    if (current_reading == VL53L0X_OUT_OF_RANGE) {
        *range = VL53L0X_OUT_OF_RANGE;
        return false;
    }
    
    if (!filter_enabled) {
        *range = current_reading;
        return true;
    }
    
    // 低通滤波器 (指数移动平均)
    const float alpha = 0.3f; // 滤波系数，越小越平滑
    
    if (first_measurement) {
        filtered_value = current_reading;
        first_measurement = false;
    } else {
        // 检测大跳变，如果变化超过50mm则重置滤波器
        int32_t diff = (int32_t)current_reading - (int32_t)filtered_value;
        if (diff > 50 || diff < -50) {
            filtered_value = current_reading; // 重置
        } else {
            // 指数移动平均滤波
            filtered_value = (uint16_t)(alpha * current_reading + (1.0f - alpha) * filtered_value);
        }
    }
    
    *range = filtered_value;
    return true;
}
