.\objects\i2c.o: Hardware\i2c.c
.\objects\i2c.o: Hardware\i2c.h
.\objects\i2c.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\i2c.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\i2c.o: .\Start\stm32f10x.h
.\objects\i2c.o: .\Start\core_cm3.h
.\objects\i2c.o: .\Start\system_stm32f10x.h
.\objects\i2c.o: .\User\stm32f10x_conf.h
.\objects\i2c.o: .\Library\stm32f10x_adc.h
.\objects\i2c.o: .\Start\stm32f10x.h
.\objects\i2c.o: .\Library\stm32f10x_bkp.h
.\objects\i2c.o: .\Library\stm32f10x_can.h
.\objects\i2c.o: .\Library\stm32f10x_cec.h
.\objects\i2c.o: .\Library\stm32f10x_crc.h
.\objects\i2c.o: .\Library\stm32f10x_dac.h
.\objects\i2c.o: .\Library\stm32f10x_dbgmcu.h
.\objects\i2c.o: .\Library\stm32f10x_dma.h
.\objects\i2c.o: .\Library\stm32f10x_exti.h
.\objects\i2c.o: .\Library\stm32f10x_flash.h
.\objects\i2c.o: .\Library\stm32f10x_fsmc.h
.\objects\i2c.o: .\Library\stm32f10x_gpio.h
.\objects\i2c.o: .\Library\stm32f10x_i2c.h
.\objects\i2c.o: .\Library\stm32f10x_iwdg.h
.\objects\i2c.o: .\Library\stm32f10x_pwr.h
.\objects\i2c.o: .\Library\stm32f10x_rcc.h
.\objects\i2c.o: .\Library\stm32f10x_rtc.h
.\objects\i2c.o: .\Library\stm32f10x_sdio.h
.\objects\i2c.o: .\Library\stm32f10x_spi.h
.\objects\i2c.o: .\Library\stm32f10x_tim.h
.\objects\i2c.o: .\Library\stm32f10x_usart.h
.\objects\i2c.o: .\Library\stm32f10x_wwdg.h
.\objects\i2c.o: .\Library\misc.h
