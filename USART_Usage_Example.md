# USART 串口通信使用示例（无库文件修改版本）

## 概述
本程序提供了完整的STM32F103 USART串口通信功能，**不修改任何库文件**，使用轮询方式实现数据收发。

## 设计原则
- ✅ **不修改库文件**: 保持原始库文件不变
- ✅ **轮询方式**: 使用轮询方式处理串口通信，避免中断冲突
- ✅ **简单可靠**: 代码简洁，易于理解和维护
- ✅ **功能完整**: 支持发送、接收、状态检查等完整功能

## 硬件连接
- USART1_TX: PA9
- USART1_RX: PA10

## 主要功能

### 1. 初始化USART
```c
UsartConfig_t usartConfig = {
    .baudRate = 115200,    // 波特率
    .dataBits = 8,         // 数据位
    .stopBits = 1,         // 停止位
    .parity = 0,           // 校验位 (0:无, 1:偶校验, 2:奇校验)
    .flowControl = 0       // 流控制 (0:无, 1:RTS/CTS)
};

if (usartInit(&usartConfig) == USART_OK) {
    // 初始化成功
}
```

### 2. 发送数据
```c
// 发送单个字节
usartSendByte(0x41);  // 发送字符 'A'

// 发送字符串
usartSendString("Hello World!\r\n");

// 发送数据数组
uint8_t data[] = {0x01, 0x02, 0x03, 0x04};
usartSendData(data, 4);
```

### 3. 接收数据（轮询方式）
```c
uint8_t receivedData;

// 方式1: 非阻塞检查
if (usartGetReceivedByte(&receivedData)) {
    // 成功接收到数据
    usartSendByte(receivedData);  // 回显
}

// 方式2: 阻塞等待
if (usartReceiveByte(&receivedData, 1000) == USART_OK) {
    // 成功接收到数据
    usartSendByte(receivedData);  // 回显
}
```

### 4. 状态检查
```c
// 检查是否可以发送数据
if (usartIsReadyToSend()) {
    usartSendByte(0x41);
}

// 检查是否有数据可接收
if (usartHasData()) {
    uint8_t data;
    usartGetReceivedByte(&data);
}
```

### 5. 中断处理（可选）
```c
// 如果需要中断方式，可以启用
usartEnableReceiveInterrupt();
usartSetReceiveCallback(myCallback);

// 在主循环中处理中断
while(1) {
    usartProcessInterrupt();  // 处理中断
    // 其他任务...
}
```

## 示例程序功能

当前main.c中的示例程序提供以下功能：

1. **初始化**: 配置USART为115200波特率，8N1格式
2. **轮询接收**: 使用轮询方式检查接收数据
3. **命令控制**: 通过串口命令控制电机
   - `'1'`: 设置电机1速度为30
   - `'2'`: 设置电机1速度为60
   - `'3'`: 设置电机1速度为90
   - `'0'`: 停止电机1
   - `'h'`: 显示帮助信息

## 使用步骤

1. 编译并下载程序到STM32F103
2. 连接USB转串口模块到PA9(TX)和PA10(RX)
3. 打开串口调试助手，设置115200波特率
4. 发送命令字符控制电机

## 优势特点

### 🔒 安全性
- **不修改库文件**: 保持系统稳定性
- **无中断冲突**: 避免与其他模块的中断冲突
- **错误处理**: 完善的错误状态返回机制

### 🚀 性能
- **实时响应**: 轮询间隔10ms，响应及时
- **低资源占用**: 不占用中断资源
- **简单高效**: 代码简洁，执行效率高

### 🛠️ 可维护性
- **模块化设计**: 功能独立，易于修改
- **清晰接口**: 函数命名规范，易于理解
- **完整文档**: 详细的注释和使用说明

## 注意事项

1. **硬件连接**: 确保正确连接串口模块的TX和RX（交叉连接）
2. **波特率设置**: 波特率必须与串口调试助手设置一致
3. **轮询频率**: 主循环中的延时影响响应速度，可根据需要调整
4. **资源占用**: 轮询方式会占用一定的CPU时间，但影响很小

## 错误处理

所有函数都返回状态码：
- `USART_OK`: 操作成功
- `USART_ERROR`: 一般错误
- `USART_TIMEOUT`: 超时错误
- `USART_BUSY`: 设备忙

建议在使用前检查返回值以确保操作成功。

## 扩展功能

如需添加更多功能，可以：
1. 添加数据缓冲机制
2. 实现协议解析
3. 添加数据校验
4. 实现多串口支持

所有扩展都可以在现有框架基础上实现，无需修改库文件。 